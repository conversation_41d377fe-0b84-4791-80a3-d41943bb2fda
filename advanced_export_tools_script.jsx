// أدوات التصدير المتقدمة الاحترافية
// Advanced Export Tools for Adobe InDesign
// المطور: Augment Agent
// التاريخ: 2025-08-30

// أنواع التصدير المتاحة
var exportTypes = {
    WEB_OPTIMIZED: "web_optimized",     // محسن للويب
    MOBILE_READY: "mobile_ready",       // جاهز للموبايل
    PRINT_READY: "print_ready",         // جاهز للطباعة
    SOCIAL_MEDIA: "social_media",       // سوشيال ميديا
    EMAIL_CAMPAIGN: "email_campaign",   // حملة إيميل
    PRESENTATION: "presentation"        // عرض تقديمي
};

// إعدادات التصدير
var exportSettings = {
    exportType: exportTypes.WEB_OPTIMIZED,
    outputFolder: "",
    filePrefix: "export",
    includeBleed: false,
    includeMarks: false,
    colorSpace: "RGB",
    resolution: 72,
    quality: "high",
    format: "PNG",
    createMultipleSizes: true,
    optimizeForSpeed: true
};

// أحجام التصدير المختلفة
var exportSizes = {
    web: [
        {name: "Desktop", width: 1920, height: 1080},
        {name: "Tablet", width: 1024, height: 768},
        {name: "Mobile", width: 375, height: 667}
    ],
    social: [
        {name: "Instagram Post", width: 1080, height: 1080},
        {name: "Facebook Post", width: 1200, height: 630},
        {name: "Twitter Header", width: 1500, height: 500},
        {name: "LinkedIn Post", width: 1200, height: 627}
    ],
    print: [
        {name: "A4 300dpi", width: 2480, height: 3508},
        {name: "A3 300dpi", width: 3508, height: 4961},
        {name: "Letter 300dpi", width: 2550, height: 3300}
    ]
};

// إعدادات الجودة
var qualityPresets = {
    web: {
        format: "PNG",
        resolution: 72,
        colorSpace: "RGB",
        compression: "automatic",
        transparency: true
    },
    mobile: {
        format: "PNG",
        resolution: 144, // Retina
        colorSpace: "RGB",
        compression: "high",
        transparency: true
    },
    print: {
        format: "PDF",
        resolution: 300,
        colorSpace: "CMYK",
        compression: "none",
        transparency: false
    },
    email: {
        format: "JPEG",
        resolution: 72,
        colorSpace: "RGB",
        compression: "medium",
        transparency: false
    }
};

function main() {
    try {
        // التحقق من وجود مستند مفتوح
        if (app.documents.length === 0) {
            alert("يرجى فتح مستند أولاً\nPlease open a document first");
            return;
        }
        
        // عرض نافذة إعدادات التصدير
        var settings = showExportDialog();
        if (!settings) return;
        
        // تطبيق الإعدادات
        Object.assign(exportSettings, settings);
        
        // بدء عملية التصدير
        performExport();
        
        alert("تم التصدير بنجاح!\nExport completed successfully!");
        
    } catch (error) {
        alert("خطأ في التصدير: " + error.message + "\nExport error: " + error.message);
    }
}

function showExportDialog() {
    var dialog = app.dialogs.add({name: "أدوات التصدير المتقدمة - Advanced Export Tools"});
    
    with (dialog.dialogColumns.add()) {
        // نوع التصدير
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "نوع التصدير:"});
        }
        var typeDropdown = dropdowns.add({stringList: [
            "محسن للويب - Web Optimized",
            "جاهز للموبايل - Mobile Ready", 
            "جاهز للطباعة - Print Ready",
            "سوشيال ميديا - Social Media",
            "حملة إيميل - Email Campaign",
            "عرض تقديمي - Presentation"
        ]});
        
        // مجلد الحفظ
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "مجلد الحفظ:"});
        }
        with (dialogRows.add()) {
            var folderGroup = dialogColumns.add();
            var folderInput = folderGroup.textEditboxes.add({editContents: "اختر مجلد الحفظ..."});
            var browseButton = folderGroup.buttons.add({name: "تصفح"});
        }
        
        // اسم الملف
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "بادئة اسم الملف:"});
        }
        var prefixInput = textEditboxes.add({editContents: "export"});
        
        // إعدادات الجودة
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "الجودة:"});
        }
        var qualityDropdown = dropdowns.add({stringList: ["عالية - High", "متوسطة - Medium", "منخفضة - Low"]});
        
        // تنسيق الملف
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "التنسيق:"});
        }
        var formatDropdown = dropdowns.add({stringList: ["PNG", "JPEG", "PDF", "SVG"]});
        
        // خيارات متقدمة
        with (dialogRows.add()) {
            var optionsGroup = dialogColumns.add();
            var multipleSizesCheck = optionsGroup.checkboxControls.add({staticLabel: "أحجام متعددة", checkedState: true});
            var optimizeCheck = optionsGroup.checkboxControls.add({staticLabel: "تحسين السرعة", checkedState: true});
            var bleedCheck = optionsGroup.checkboxControls.add({staticLabel: "تضمين النزيف", checkedState: false});
        }
        
        // دقة الوضوح
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "دقة الوضوح (DPI):"});
        }
        var resolutionInput = integerEditboxes.add({editValue: 72});
    }
    
    // معالج زر التصفح
    browseButton.onClick = function() {
        var folder = Folder.selectDialog("اختر مجلد الحفظ");
        if (folder) {
            folderInput.editContents = folder.fsName;
        }
    };
    
    var result = dialog.show();
    if (result) {
        var types = [exportTypes.WEB_OPTIMIZED, exportTypes.MOBILE_READY, exportTypes.PRINT_READY,
                    exportTypes.SOCIAL_MEDIA, exportTypes.EMAIL_CAMPAIGN, exportTypes.PRESENTATION];
        var qualities = ["high", "medium", "low"];
        var formats = ["PNG", "JPEG", "PDF", "SVG"];
        
        var settings = {
            exportType: types[typeDropdown.selectedIndex],
            outputFolder: folderInput.editContents,
            filePrefix: prefixInput.editContents,
            quality: qualities[qualityDropdown.selectedIndex],
            format: formats[formatDropdown.selectedIndex],
            createMultipleSizes: multipleSizesCheck.checkedState,
            optimizeForSpeed: optimizeCheck.checkedState,
            includeBleed: bleedCheck.checkedState,
            resolution: resolutionInput.editValue
        };
        
        dialog.destroy();
        return settings;
    }
    
    dialog.destroy();
    return null;
}

function performExport() {
    var doc = app.activeDocument;
    
    // إنشاء مجلد الحفظ إذا لم يكن موجوداً
    var outputFolder = new Folder(exportSettings.outputFolder);
    if (!outputFolder.exists) {
        outputFolder.create();
    }
    
    // تحديد الأحجام المطلوبة
    var sizes = getSizesForExportType(exportSettings.exportType);
    
    if (exportSettings.createMultipleSizes && sizes.length > 1) {
        // تصدير بأحجام متعددة
        for (var i = 0; i < sizes.length; i++) {
            exportAtSize(doc, sizes[i], outputFolder);
        }
    } else {
        // تصدير بحجم واحد
        var defaultSize = sizes[0] || {name: "Default", width: 1920, height: 1080};
        exportAtSize(doc, defaultSize, outputFolder);
    }
    
    // إنشاء ملف معلومات التصدير
    createExportInfo(outputFolder, sizes);
}

function getSizesForExportType(exportType) {
    switch (exportType) {
        case exportTypes.WEB_OPTIMIZED:
        case exportTypes.MOBILE_READY:
        case exportTypes.PRESENTATION:
            return exportSizes.web;
        case exportTypes.SOCIAL_MEDIA:
            return exportSizes.social;
        case exportTypes.PRINT_READY:
            return exportSizes.print;
        case exportTypes.EMAIL_CAMPAIGN:
            return [exportSizes.web[2]]; // Mobile size for email
        default:
            return exportSizes.web;
    }
}

function exportAtSize(doc, size, outputFolder) {
    try {
        var fileName = exportSettings.filePrefix + "_" + size.name.replace(/\s+/g, "_");
        var filePath = outputFolder.fsName + "/" + fileName;
        
        switch (exportSettings.format.toUpperCase()) {
            case "PNG":
                exportAsPNG(doc, filePath, size);
                break;
            case "JPEG":
                exportAsJPEG(doc, filePath, size);
                break;
            case "PDF":
                exportAsPDF(doc, filePath, size);
                break;
            case "SVG":
                exportAsSVG(doc, filePath, size);
                break;
        }
        
    } catch (error) {
        throw new Error("فشل في تصدير " + size.name + ": " + error.message);
    }
}

function exportAsPNG(doc, filePath, size) {
    var exportFormat = ExportFormat.PNG_FORMAT;
    var exportParams = app.pngExportPreferences;
    
    // إعدادات PNG
    with (exportParams) {
        pngQuality = getPNGQuality(exportSettings.quality);
        exportResolution = exportSettings.resolution;
        pngColorSpace = PNGColorSpaceEnum.RGB;
        transparentBackground = true;
        antiAlias = true;
        useDocumentBleeds = exportSettings.includeBleed;
        
        // تحديد الحجم إذا كان مطلوباً
        if (size.width && size.height) {
            horizontalScale = (size.width / parseFloat(doc.documentPreferences.pageWidth)) * 100;
            verticalScale = (size.height / parseFloat(doc.documentPreferences.pageHeight)) * 100;
        }
    }
    
    var exportFile = new File(filePath + ".png");
    doc.exportFile(exportFormat, exportFile);
}

function exportAsJPEG(doc, filePath, size) {
    var exportFormat = ExportFormat.JPG;
    var exportParams = app.jpegExportPreferences;
    
    // إعدادات JPEG
    with (exportParams) {
        jpegQuality = getJPEGQuality(exportSettings.quality);
        exportResolution = exportSettings.resolution;
        jpegColorSpace = JpegColorSpaceEnum.RGB;
        antiAlias = true;
        useDocumentBleeds = exportSettings.includeBleed;
        
        if (size.width && size.height) {
            horizontalScale = (size.width / parseFloat(doc.documentPreferences.pageWidth)) * 100;
            verticalScale = (size.height / parseFloat(doc.documentPreferences.pageHeight)) * 100;
        }
    }
    
    var exportFile = new File(filePath + ".jpg");
    doc.exportFile(exportFormat, exportFile);
}

function exportAsPDF(doc, filePath, size) {
    var exportFormat = ExportFormat.PDF_TYPE;
    var exportParams = app.pdfExportPreferences;
    
    // إعدادات PDF
    with (exportParams) {
        pageRange = PageRange.ALL_PAGES;
        acrobatCompatibility = AcrobatCompatibility.ACROBAT_8;
        exportGuidesAndGrids = false;
        exportLayers = false;
        exportNonprintingObjects = false;
        exportReaderSpreads = false;
        generateThumbnails = true;
        ignoreSpreadOverrides = false;
        includeBookmarks = false;
        includeHyperlinks = true;
        includeICCProfiles = true;
        includeStructure = false;
        interactiveElementsOption = InteractiveElementsOptions.DO_NOT_INCLUDE;
        
        // إعدادات الضغط
        colorBitmapCompression = BitmapCompression.JPEG_HIGH;
        colorBitmapQuality = CompressionQuality.HIGH;
        grayscaleBitmapCompression = BitmapCompression.JPEG_HIGH;
        grayscaleBitmapQuality = CompressionQuality.HIGH;
        monochromeBitmapCompression = MonoBitmapCompression.CCIT4;
        
        // إعدادات الألوان
        if (exportSettings.exportType === exportTypes.PRINT_READY) {
            colorConversionID = ColorConversion.CONVERT_TO_DESTINATION_PRESERVE_NUMBERS;
            destinationProfileID = "Coated FOGRA39 (ISO 12647-2:2004)";
        } else {
            colorConversionID = ColorConversion.CONVERT_TO_DESTINATION;
            destinationProfileID = "sRGB IEC61966-2.1";
        }
        
        useDocumentBleeds = exportSettings.includeBleed;
        cropMarks = exportSettings.includeMarks;
        registrationMarks = exportSettings.includeMarks;
    }
    
    var exportFile = new File(filePath + ".pdf");
    doc.exportFile(exportFormat, exportFile);
}

function exportAsSVG(doc, filePath, size) {
    // SVG export (محدود في InDesign)
    try {
        var exportFormat = ExportFormat.SVG;
        var exportParams = app.svgExportPreferences;
        
        with (exportParams) {
            exportResolution = exportSettings.resolution;
            svgId = SVGIdGeneration.LAYER_NAMES;
            cssPropertyLocation = CssPropertyLocation.STYLE_ATTRIBUTES;
            dtd = SVGDtdFormat.SVG1_1;
            fontSubsetting = SVGFontSubsetting.NONE;
            googleViewerPrefs = false;
            renderingStyle = RenderingStyle.GEOMETRIC_PRECISION;
        }
        
        var exportFile = new File(filePath + ".svg");
        doc.exportFile(exportFormat, exportFile);
        
    } catch (error) {
        // إذا فشل SVG، قم بالتصدير كـ PNG بدلاً من ذلك
        exportAsPNG(doc, filePath + "_svg_fallback", size);
    }
}

function getPNGQuality(quality) {
    switch (quality) {
        case "high": return PNGQualityEnum.HIGH;
        case "medium": return PNGQualityEnum.MEDIUM;
        case "low": return PNGQualityEnum.LOW;
        default: return PNGQualityEnum.HIGH;
    }
}

function getJPEGQuality(quality) {
    switch (quality) {
        case "high": return JPEGOptionsQuality.HIGH;
        case "medium": return JPEGOptionsQuality.MEDIUM;
        case "low": return JPEGOptionsQuality.LOW;
        default: return JPEGOptionsQuality.HIGH;
    }
}

function createExportInfo(outputFolder, sizes) {
    try {
        var infoFile = new File(outputFolder.fsName + "/export_info.txt");
        infoFile.open("w");
        
        infoFile.writeln("معلومات التصدير - Export Information");
        infoFile.writeln("=====================================");
        infoFile.writeln("التاريخ - Date: " + new Date().toString());
        infoFile.writeln("نوع التصدير - Export Type: " + exportSettings.exportType);
        infoFile.writeln("التنسيق - Format: " + exportSettings.format);
        infoFile.writeln("الجودة - Quality: " + exportSettings.quality);
        infoFile.writeln("دقة الوضوح - Resolution: " + exportSettings.resolution + " DPI");
        infoFile.writeln("تضمين النزيف - Include Bleed: " + (exportSettings.includeBleed ? "نعم" : "لا"));
        infoFile.writeln("");
        infoFile.writeln("الأحجام المُصدرة - Exported Sizes:");
        
        for (var i = 0; i < sizes.length; i++) {
            infoFile.writeln("- " + sizes[i].name + ": " + sizes[i].width + "x" + sizes[i].height);
        }
        
        infoFile.close();
        
    } catch (error) {
        // تجاهل أخطاء إنشاء ملف المعلومات
    }
}

// تشغيل السكربت
main();
