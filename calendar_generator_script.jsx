// سكربت إنشاء التقاويم المخصصة الاحترافية
// Professional Custom Calendar Generator for Adobe InDesign
// المطور: Augment Agent
// التاريخ: 2025-08-30

// أنواع التقاويم
var calendarTypes = {
    MONTHLY: "monthly",         // تقويم شهري
    YEARLY: "yearly",          // تقويم سنوي
    WEEKLY: "weekly",          // تقويم أسبوعي
    DESK: "desk",              // تقويم مكتبي
    WALL: "wall",              // تقويم حائط
    POCKET: "pocket"           // تقويم جيب
};

// إعدادات التقويم
var calendarSettings = {
    year: 2025,                        // السنة
    startMonth: 1,                     // الشهر الأول
    endMonth: 12,                      // الشهر الأخير
    calendarType: calendarTypes.MONTHLY,
    language: "arabic",                // اللغة (arabic/english/both)
    weekStartsOn: "saturday",          // بداية الأسبوع
    includeHijri: true,               // تضمين التاريخ الهجري
    includeHolidays: true,            // تضمين العطل
    includeNotes: true                // تضمين مساحة للملاحظات
};

// أحجام التقاويم
var calendarSizes = {
    monthly: {width: "29.7cm", height: "21cm"},      // A4 أفقي
    yearly: {width: "42cm", height: "29.7cm"},       // A3 أفقي
    weekly: {width: "21cm", height: "29.7cm"},       // A4 عمودي
    desk: {width: "15cm", height: "10cm"},           // مكتبي صغير
    wall: {width: "50cm", height: "35cm"},           // حائط كبير
    pocket: {width: "10cm", height: "15cm"}          // جيب صغير
};

// أسماء الشهور
var monthNames = {
    arabic: ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
             "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"],
    english: ["January", "February", "March", "April", "May", "June",
              "July", "August", "September", "October", "November", "December"]
};

// أسماء أيام الأسبوع
var dayNames = {
    arabic: ["السبت", "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة"],
    english: ["Saturday", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
};

// مخططات الألوان
var colorSchemes = {
    classic: {
        primary: [0, 0, 0, 100],       // أسود
        secondary: [0, 0, 0, 20],      // رمادي
        accent: [0, 100, 100, 0],      // أحمر
        background: [0, 0, 0, 0],      // أبيض
        weekend: [0, 0, 0, 10]         // رمادي فاتح للعطل
    },
    modern: {
        primary: [100, 50, 0, 0],      // أزرق
        secondary: [50, 25, 0, 0],     // أزرق فاتح
        accent: [0, 80, 100, 0],       // برتقالي
        background: [20, 10, 0, 0],    // أزرق فاتح جداً
        weekend: [0, 20, 40, 0]        // كريمي
    },
    nature: {
        primary: [50, 0, 100, 10],     // أخضر داكن
        secondary: [30, 0, 80, 0],     // أخضر فاتح
        accent: [0, 50, 100, 0],       // برتقالي
        background: [15, 0, 30, 0],    // أخضر فاتح جداً
        weekend: [20, 0, 40, 0]        // أخضر كريمي
    }
};

function main() {
    try {
        // عرض نافذة إعدادات التقويم
        var settings = showCalendarDialog();
        if (!settings) return;
        
        // تطبيق الإعدادات
        Object.assign(calendarSettings, settings);
        
        // إنشاء التقويم
        var doc = createCalendarDocument();
        
        switch (calendarSettings.calendarType) {
            case calendarTypes.MONTHLY:
                createMonthlyCalendar(doc);
                break;
            case calendarTypes.YEARLY:
                createYearlyCalendar(doc);
                break;
            case calendarTypes.WEEKLY:
                createWeeklyCalendar(doc);
                break;
            case calendarTypes.DESK:
                createDeskCalendar(doc);
                break;
            case calendarTypes.WALL:
                createWallCalendar(doc);
                break;
            case calendarTypes.POCKET:
                createPocketCalendar(doc);
                break;
        }
        
        alert("تم إنشاء التقويم بنجاح!\nCalendar created successfully!");
        
    } catch (error) {
        alert("خطأ: " + error.message + "\nError: " + error.message);
    }
}

function showCalendarDialog() {
    var dialog = app.dialogs.add({name: "إعدادات التقويم - Calendar Settings"});
    
    with (dialog.dialogColumns.add()) {
        // السنة
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "السنة:"});
        }
        var yearInput = integerEditboxes.add({editValue: 2025});
        
        // نوع التقويم
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "نوع التقويم:"});
        }
        var typeDropdown = dropdowns.add({stringList: [
            "شهري - Monthly", "سنوي - Yearly", "أسبوعي - Weekly",
            "مكتبي - Desk", "حائط - Wall", "جيب - Pocket"
        ]});
        
        // مخطط الألوان
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "مخطط الألوان:"});
        }
        var colorDropdown = dropdowns.add({stringList: [
            "كلاسيكي - Classic", "عصري - Modern", "طبيعة - Nature"
        ]});
        
        // اللغة
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "اللغة:"});
        }
        var langDropdown = dropdowns.add({stringList: [
            "عربي - Arabic", "إنجليزي - English", "ثنائي - Both"
        ]});
        
        // خيارات إضافية
        with (dialogRows.add()) {
            var optionsGroup = dialogColumns.add();
            var hijriCheck = optionsGroup.checkboxControls.add({staticLabel: "تاريخ هجري", checkedState: true});
            var holidaysCheck = optionsGroup.checkboxControls.add({staticLabel: "العطل الرسمية", checkedState: true});
            var notesCheck = optionsGroup.checkboxControls.add({staticLabel: "مساحة ملاحظات", checkedState: true});
        }
    }
    
    var result = dialog.show();
    if (result) {
        var types = [calendarTypes.MONTHLY, calendarTypes.YEARLY, calendarTypes.WEEKLY,
                    calendarTypes.DESK, calendarTypes.WALL, calendarTypes.POCKET];
        var colors = ["classic", "modern", "nature"];
        var languages = ["arabic", "english", "both"];
        
        var settings = {
            year: yearInput.editValue,
            calendarType: types[typeDropdown.selectedIndex],
            colorScheme: colors[colorDropdown.selectedIndex],
            language: languages[langDropdown.selectedIndex],
            includeHijri: hijriCheck.checkedState,
            includeHolidays: holidaysCheck.checkedState,
            includeNotes: notesCheck.checkedState
        };
        
        dialog.destroy();
        return settings;
    }
    
    dialog.destroy();
    return null;
}

function createCalendarDocument() {
    var doc = app.documents.add();
    var size = calendarSizes[calendarSettings.calendarType];
    
    with (doc.documentPreferences) {
        pageWidth = size.width;
        pageHeight = size.height;
        facingPages = false;
        pagesPerDocument = 12; // 12 شهر
    }
    
    // إعداد الهوامش
    doc.masterSpreads[0].pages.everyItem().marginPreferences.properties = {
        top: "1cm",
        bottom: "1cm",
        left: "1cm",
        right: "1cm"
    };
    
    return doc;
}

function createMonthlyCalendar(doc) {
    var colors = colorSchemes[calendarSettings.colorScheme || "classic"];
    
    for (var month = 0; month < 12; month++) {
        var page = doc.pages[month];
        createMonthPage(doc, page, month + 1, colors);
    }
}

function createMonthPage(doc, page, monthNumber, colors) {
    var size = calendarSizes[calendarSettings.calendarType];
    
    // رأس الشهر
    var headerFrame = page.textFrames.add();
    headerFrame.geometricBounds = [1, 1, 4, parseFloat(size.width) - 1];
    
    var monthNameAr = monthNames.arabic[monthNumber - 1];
    var monthNameEn = monthNames.english[monthNumber - 1];
    var headerText = "";
    
    switch (calendarSettings.language) {
        case "arabic":
            headerText = monthNameAr + " " + calendarSettings.year;
            break;
        case "english":
            headerText = monthNameEn + " " + calendarSettings.year;
            break;
        case "both":
            headerText = monthNameAr + " - " + monthNameEn + " " + calendarSettings.year;
            break;
    }
    
    headerFrame.contents = headerText;
    with (headerFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial-BoldMT");
        pointSize = "24pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "Header", colors.primary);
    }
    
    // خلفية الرأس
    var headerBg = page.rectangles.add();
    headerBg.geometricBounds = headerFrame.geometricBounds;
    headerBg.fillColor = createColorFromValues(doc, "HeaderBg", colors.secondary);
    headerBg.strokeColor = doc.colors.item("None");
    headerBg.sendToBack();
    
    // شبكة الأيام
    createMonthGrid(doc, page, monthNumber, colors);
    
    // مساحة الملاحظات
    if (calendarSettings.includeNotes) {
        createNotesArea(doc, page, colors);
    }
}

function createMonthGrid(doc, page, monthNumber, colors) {
    var size = calendarSizes[calendarSettings.calendarType];
    var gridTop = 5;
    var gridHeight = parseFloat(size.height) - 8; // ترك مساحة للملاحظات
    var gridWidth = parseFloat(size.width) - 2;
    
    // رؤوس أيام الأسبوع
    var dayHeaderHeight = 1.5;
    for (var day = 0; day < 7; day++) {
        var dayHeader = page.textFrames.add();
        var dayWidth = gridWidth / 7;
        dayHeader.geometricBounds = [
            gridTop,
            1 + day * dayWidth,
            gridTop + dayHeaderHeight,
            1 + (day + 1) * dayWidth
        ];
        
        var dayNameAr = dayNames.arabic[day];
        var dayNameEn = dayNames.english[day];
        var dayText = calendarSettings.language === "arabic" ? dayNameAr :
                     calendarSettings.language === "english" ? dayNameEn :
                     dayNameAr + "\n" + dayNameEn;
        
        dayHeader.contents = dayText;
        with (dayHeader.parentStory.characters.everyItem()) {
            appliedFont = app.fonts.item("Arial-BoldMT");
            pointSize = "12pt";
            justification = Justification.CENTER_ALIGN;
            fillColor = createColorFromValues(doc, "DayHeader", colors.primary);
        }
        
        // خلفية رأس اليوم
        var dayHeaderBg = page.rectangles.add();
        dayHeaderBg.geometricBounds = dayHeader.geometricBounds;
        dayHeaderBg.fillColor = createColorFromValues(doc, "DayHeaderBg", colors.secondary);
        dayHeaderBg.strokeColor = createColorFromValues(doc, "Border", colors.primary);
        dayHeaderBg.strokeWeight = "0.5pt";
        dayHeaderBg.sendToBack();
    }
    
    // شبكة التواريخ
    var daysInMonth = getDaysInMonth(monthNumber, calendarSettings.year);
    var firstDayOfWeek = getFirstDayOfWeek(monthNumber, calendarSettings.year);
    var cellHeight = (gridHeight - dayHeaderHeight) / 6; // 6 أسابيع كحد أقصى
    var cellWidth = gridWidth / 7;
    
    var currentDate = 1;
    
    for (var week = 0; week < 6; week++) {
        for (var day = 0; day < 7; day++) {
            var cellTop = gridTop + dayHeaderHeight + week * cellHeight;
            var cellLeft = 1 + day * cellWidth;
            
            // إنشاء خلية اليوم
            var dayCell = page.rectangles.add();
            dayCell.geometricBounds = [
                cellTop,
                cellLeft,
                cellTop + cellHeight,
                cellLeft + cellWidth
            ];
            dayCell.strokeColor = createColorFromValues(doc, "CellBorder", colors.primary);
            dayCell.strokeWeight = "0.25pt";
            
            // تحديد ما إذا كانت الخلية تحتوي على تاريخ
            var shouldShowDate = (week === 0 && day >= firstDayOfWeek) || 
                               (week > 0 && currentDate <= daysInMonth);
            
            if (shouldShowDate && currentDate <= daysInMonth) {
                // لون الخلفية (مختلف للعطل)
                var isWeekend = (day === 5 || day === 6); // الجمعة والسبت
                dayCell.fillColor = isWeekend ? 
                    createColorFromValues(doc, "Weekend", colors.weekend) :
                    createColorFromValues(doc, "Weekday", colors.background);
                
                // رقم اليوم
                var dateFrame = dayCell.textFrames.add();
                dateFrame.geometricBounds = [
                    cellTop + 0.1,
                    cellLeft + 0.1,
                    cellTop + 0.8,
                    cellLeft + cellWidth - 0.1
                ];
                dateFrame.contents = String(currentDate);
                with (dateFrame.parentStory.characters.everyItem()) {
                    appliedFont = app.fonts.item("Arial-BoldMT");
                    pointSize = "14pt";
                    justification = Justification.LEFT_ALIGN;
                    fillColor = isWeekend ? 
                        createColorFromValues(doc, "WeekendText", colors.accent) :
                        createColorFromValues(doc, "WeekdayText", colors.primary);
                }
                
                // مساحة للملاحظات في كل يوم
                if (calendarSettings.includeNotes) {
                    var noteArea = dayCell.textFrames.add();
                    noteArea.geometricBounds = [
                        cellTop + 0.9,
                        cellLeft + 0.1,
                        cellTop + cellHeight - 0.1,
                        cellLeft + cellWidth - 0.1
                    ];
                    noteArea.contents = ""; // فارغ للملاحظات
                    noteArea.strokeColor = createColorFromValues(doc, "NoteBorder", colors.secondary);
                    noteArea.strokeWeight = "0.25pt";
                }
                
                currentDate++;
            } else {
                dayCell.fillColor = createColorFromValues(doc, "EmptyCell", colors.background);
            }
        }
    }
}

function createNotesArea(doc, page, colors) {
    var size = calendarSizes[calendarSettings.calendarType];
    
    var notesFrame = page.textFrames.add();
    notesFrame.geometricBounds = [
        parseFloat(size.height) - 6,
        1,
        parseFloat(size.height) - 1,
        parseFloat(size.width) - 1
    ];
    
    notesFrame.contents = "الملاحظات - Notes:\n\n\n\n";
    with (notesFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial");
        pointSize = "12pt";
        justification = Justification.LEFT_ALIGN;
        fillColor = createColorFromValues(doc, "NotesText", colors.primary);
    }
    
    // خلفية الملاحظات
    var notesBg = page.rectangles.add();
    notesBg.geometricBounds = notesFrame.geometricBounds;
    notesBg.fillColor = createColorFromValues(doc, "NotesBg", colors.background);
    notesBg.strokeColor = createColorFromValues(doc, "NotesBorder", colors.secondary);
    notesBg.strokeWeight = "1pt";
    notesBg.sendToBack();
}

function createYearlyCalendar(doc) {
    var colors = colorSchemes[calendarSettings.colorScheme || "modern"];
    var page = doc.pages[0];
    
    // عنوان السنة
    var yearTitle = page.textFrames.add();
    yearTitle.geometricBounds = [1, 1, 4, 41];
    yearTitle.contents = "تقويم " + calendarSettings.year + "\nCalendar " + calendarSettings.year;
    with (yearTitle.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial-BoldMT");
        pointSize = "36pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "YearTitle", colors.primary);
    }
    
    // شبكة الشهور (4x3)
    var monthWidth = 38 / 4;
    var monthHeight = 24 / 3;
    
    for (var month = 0; month < 12; month++) {
        var row = Math.floor(month / 4);
        var col = month % 4;
        
        var monthX = 2 + col * monthWidth;
        var monthY = 5 + row * monthHeight;
        
        createMiniMonth(doc, page, month + 1, monthX, monthY, monthWidth, monthHeight, colors);
    }
}

function createMiniMonth(doc, page, monthNumber, x, y, width, height, colors) {
    // عنوان الشهر
    var monthHeader = page.textFrames.add();
    monthHeader.geometricBounds = [y, x, y + 1.5, x + width];
    
    var monthName = calendarSettings.language === "arabic" ? monthNames.arabic[monthNumber - 1] :
                   calendarSettings.language === "english" ? monthNames.english[monthNumber - 1] :
                   monthNames.arabic[monthNumber - 1] + " - " + monthNames.english[monthNumber - 1];
    
    monthHeader.contents = monthName;
    with (monthHeader.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial-BoldMT");
        pointSize = "10pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "MonthHeader", colors.primary);
    }
    
    // شبكة مصغرة للأيام
    var daysInMonth = getDaysInMonth(monthNumber, calendarSettings.year);
    var firstDay = getFirstDayOfWeek(monthNumber, calendarSettings.year);
    
    var dayWidth = width / 7;
    var dayHeight = (height - 1.5) / 6;
    var currentDate = 1;
    
    for (var week = 0; week < 6; week++) {
        for (var day = 0; day < 7; day++) {
            var dayX = x + day * dayWidth;
            var dayY = y + 1.5 + week * dayHeight;
            
            var dayCell = page.rectangles.add();
            dayCell.geometricBounds = [dayY, dayX, dayY + dayHeight, dayX + dayWidth];
            dayCell.strokeColor = createColorFromValues(doc, "MiniCellBorder", colors.secondary);
            dayCell.strokeWeight = "0.1pt";
            
            var shouldShowDate = (week === 0 && day >= firstDay) || 
                               (week > 0 && currentDate <= daysInMonth);
            
            if (shouldShowDate && currentDate <= daysInMonth) {
                dayCell.fillColor = createColorFromValues(doc, "MiniCell", colors.background);
                
                var dateText = dayCell.textFrames.add();
                dateText.contents = String(currentDate);
                with (dateText.parentStory.characters.everyItem()) {
                    appliedFont = app.fonts.item("Arial");
                    pointSize = "6pt";
                    justification = Justification.CENTER_ALIGN;
                    fillColor = createColorFromValues(doc, "MiniDate", colors.primary);
                }
                
                currentDate++;
            } else {
                dayCell.fillColor = createColorFromValues(doc, "EmptyMiniCell", colors.background);
            }
        }
    }
}

// دوال مساعدة للتاريخ
function getDaysInMonth(month, year) {
    return new Date(year, month, 0).getDate();
}

function getFirstDayOfWeek(month, year) {
    var firstDay = new Date(year, month - 1, 1).getDay();
    // تحويل لبداية السبت (0 = سبت، 1 = أحد، إلخ)
    return (firstDay + 1) % 7;
}

function createColorFromValues(doc, colorName, colorValues) {
    try {
        return doc.colors.item(colorName);
    } catch (e) {
        var color = doc.colors.add();
        color.name = colorName;
        color.model = ColorModel.PROCESS;
        color.colorValue = colorValues;
        return color;
    }
}

// تشغيل السكربت
main();
