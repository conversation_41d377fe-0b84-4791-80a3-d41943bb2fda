// سكربت إنشاء الفلايرز الإعلانية الاحترافية
// Professional Flyer Generator for Adobe InDesign
// المطور: Augment Agent
// التاريخ: 2025-08-30

// أنواع الفلايرز المتاحة
var flyerTypes = {
    SALE: "sale",           // فلاير تخفيضات
    EVENT: "event",         // فلاير فعالية
    RESTAURANT: "restaurant", // فلاير مطعم
    BUSINESS: "business",   // فلاير تجاري
    REAL_ESTATE: "realestate" // فلاير عقاري
};

// إعدادات الفلاير
var flyerSettings = {
    width: "21cm",          // عرض A4
    height: "29.7cm",       // ارتفاع A4
    margins: {
        top: "1cm",
        bottom: "1cm",
        left: "1cm", 
        right: "1cm"
    },
    flyerType: flyerTypes.SALE, // نوع الفلاير الافتراضي
    orientation: "portrait"      // اتجاه الصفحة
};

// إعدادات التصميم
var designSettings = {
    mainTitle: "عرض خاص",
    subtitle: "خصم يصل إلى 50%",
    description: "تسوق الآن واحصل على أفضل العروض",
    contactInfo: "اتصل بنا: 123-456-7890",
    website: "www.company.com",
    address: "العنوان: شارع التجارة، المدينة",
    logoText: "شعار الشركة",
    includeQR: true,
    includeBorder: true,
    backgroundPattern: true
};

// ألوان التصميم
var colorSchemes = {
    sale: {
        primary: [0, 100, 100, 0],    // أحمر
        secondary: [0, 0, 0, 10],     // رمادي فاتح
        accent: [100, 100, 0, 0],     // أصفر
        text: [0, 0, 0, 100]          // أسود
    },
    event: {
        primary: [100, 0, 100, 0],    // أزرق
        secondary: [50, 0, 50, 0],    // أزرق فاتح
        accent: [0, 50, 100, 0],      // برتقالي
        text: [0, 0, 0, 100]          // أسود
    },
    restaurant: {
        primary: [0, 50, 100, 10],    // بني
        secondary: [0, 20, 50, 0],    // كريمي
        accent: [0, 100, 100, 0],     // أحمر
        text: [0, 0, 0, 100]          // أسود
    },
    business: {
        primary: [100, 50, 0, 20],    // أزرق داكن
        secondary: [50, 25, 0, 0],    // أزرق فاتح
        accent: [0, 80, 100, 0],      // برتقالي
        text: [0, 0, 0, 100]          // أسود
    },
    realestate: {
        primary: [0, 0, 0, 80],       // رمادي داكن
        secondary: [0, 0, 0, 20],     // رمادي فاتح
        accent: [0, 100, 100, 0],     // أحمر
        text: [0, 0, 0, 100]          // أسود
    }
};

// إعدادات النصوص
var textStyles = {
    titleFont: "Arial-BoldMT",
    titleSize: "36pt",
    subtitleFont: "Arial-BoldMT",
    subtitleSize: "24pt",
    bodyFont: "Arial",
    bodySize: "14pt",
    contactFont: "Arial",
    contactSize: "12pt"
};

function main() {
    try {
        // عرض نافذة اختيار نوع الفلاير
        var selectedType = showFlyerTypeDialog();
        if (!selectedType) return;
        
        flyerSettings.flyerType = selectedType;
        
        // إنشاء الفلاير
        var doc = createFlyerDocument();
        
        switch (selectedType) {
            case flyerTypes.SALE:
                createSaleFlyer(doc);
                break;
            case flyerTypes.EVENT:
                createEventFlyer(doc);
                break;
            case flyerTypes.RESTAURANT:
                createRestaurantFlyer(doc);
                break;
            case flyerTypes.BUSINESS:
                createBusinessFlyer(doc);
                break;
            case flyerTypes.REAL_ESTATE:
                createRealEstateFlyer(doc);
                break;
        }
        
        alert("تم إنشاء الفلاير بنجاح!\nFlyer created successfully!");
        
    } catch (error) {
        alert("خطأ: " + error.message + "\nError: " + error.message);
    }
}

function showFlyerTypeDialog() {
    var dialog = app.dialogs.add({name: "اختر نوع الفلاير - Choose Flyer Type"});
    
    with (dialog.dialogColumns.add()) {
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "اختر نوع الفلاير:"});
        }
        
        var typeGroup = radiobuttonGroups.add();
        with (typeGroup) {
            with (dialogRows.add()) {
                radiobuttonControls.add({staticLabel: "فلاير تخفيضات - Sale Flyer", checkedState: true});
            }
            with (dialogRows.add()) {
                radiobuttonControls.add({staticLabel: "فلاير فعالية - Event Flyer"});
            }
            with (dialogRows.add()) {
                radiobuttonControls.add({staticLabel: "فلاير مطعم - Restaurant Flyer"});
            }
            with (dialogRows.add()) {
                radiobuttonControls.add({staticLabel: "فلاير تجاري - Business Flyer"});
            }
            with (dialogRows.add()) {
                radiobuttonControls.add({staticLabel: "فلاير عقاري - Real Estate Flyer"});
            }
        }
    }
    
    var result = dialog.show();
    if (result) {
        var selectedIndex = typeGroup.selectedButton;
        var types = [flyerTypes.SALE, flyerTypes.EVENT, flyerTypes.RESTAURANT, flyerTypes.BUSINESS, flyerTypes.REAL_ESTATE];
        dialog.destroy();
        return types[selectedIndex];
    }
    
    dialog.destroy();
    return null;
}

function createFlyerDocument() {
    var doc = app.documents.add();
    
    with (doc.documentPreferences) {
        pageWidth = flyerSettings.width;
        pageHeight = flyerSettings.height;
        facingPages = false;
        pagesPerDocument = 1;
    }
    
    with (doc.pages[0].marginPreferences) {
        top = flyerSettings.margins.top;
        bottom = flyerSettings.margins.bottom;
        left = flyerSettings.margins.left;
        right = flyerSettings.margins.right;
    }
    
    return doc;
}

function createSaleFlyer(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes.sale;
    
    // خلفية الفلاير
    createFlyerBackground(doc, page, colors);
    
    // العنوان الرئيسي
    var titleFrame = page.textFrames.add();
    titleFrame.geometricBounds = [2, 2, 6, 19];
    titleFrame.contents = "عرض خاص\nSPECIAL OFFER";
    
    with (titleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = textStyles.titleSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "SaleTitle", colors.primary);
    }
    
    // العنوان الفرعي
    var subtitleFrame = page.textFrames.add();
    subtitleFrame.geometricBounds = [6.5, 2, 10, 19];
    subtitleFrame.contents = "خصم يصل إلى 50%\nUp to 50% OFF";
    
    with (subtitleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.subtitleFont);
        pointSize = textStyles.subtitleSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "SaleSubtitle", colors.accent);
    }
    
    // منطقة المنتجات
    createProductShowcase(doc, page, colors, 11, 20);
    
    // معلومات الاتصال
    createContactSection(doc, page, colors, 21, 27);
    
    // إضافة حدود وزخارف
    if (designSettings.includeBorder) {
        addDecorativeBorder(doc, page, colors);
    }
}

function createEventFlyer(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes.event;
    
    // تخطيط مختلف للفعاليات
    createFlyerBackground(doc, page, colors);
    
    // عنوان الفعالية
    var eventTitle = page.textFrames.add();
    eventTitle.geometricBounds = [3, 2, 8, 19];
    eventTitle.contents = "فعالية خاصة\nSPECIAL EVENT";
    
    with (eventTitle.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = "32pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "EventTitle", colors.primary);
    }
    
    // تاريخ ووقت الفعالية
    var dateTimeFrame = page.textFrames.add();
    dateTimeFrame.geometricBounds = [9, 2, 12, 19];
    dateTimeFrame.contents = "التاريخ: 15 سبتمبر 2025\nالوقت: 7:00 مساءً\nDate: September 15, 2025\nTime: 7:00 PM";
    
    with (dateTimeFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "16pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "EventDateTime", colors.text);
    }
    
    // تفاصيل الفعالية
    var detailsFrame = page.textFrames.add();
    detailsFrame.geometricBounds = [13, 2, 20, 19];
    detailsFrame.contents = "انضم إلينا في فعالية استثنائية مليئة بالمفاجآت والعروض الترفيهية.\n\nJoin us for an exceptional event full of surprises and entertainment.";
    
    with (detailsFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = textStyles.bodySize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "EventDetails", colors.text);
    }
    
    // معلومات الاتصال
    createContactSection(doc, page, colors, 21, 27);
}

function createRestaurantFlyer(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes.restaurant;
    
    createFlyerBackground(doc, page, colors);
    
    // اسم المطعم
    var restaurantName = page.textFrames.add();
    restaurantName.geometricBounds = [2, 2, 6, 19];
    restaurantName.contents = "مطعم الذواقة\nGourmet Restaurant";
    
    with (restaurantName.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = "30pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "RestaurantName", colors.primary);
    }
    
    // قائمة الطعام المميزة
    var menuFrame = page.textFrames.add();
    menuFrame.geometricBounds = [7, 2, 18, 19];
    menuFrame.contents = "قائمة طعام مميزة\nSpecial Menu\n\n" +
                        "• الطبق الرئيسي - Main Course: 150 ج.م\n" +
                        "• المقبلات - Appetizers: 75 ج.م\n" +
                        "• الحلويات - Desserts: 50 ج.م\n" +
                        "• المشروبات - Beverages: 25 ج.م\n\n" +
                        "خصم 20% على الوجبات العائلية\n20% off family meals";
    
    with (menuFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "13pt";
        justification = Justification.LEFT_ALIGN;
        fillColor = createColorFromValues(doc, "RestaurantMenu", colors.text);
    }
    
    // ساعات العمل
    var hoursFrame = page.textFrames.add();
    hoursFrame.geometricBounds = [19, 2, 22, 19];
    hoursFrame.contents = "ساعات العمل: يومياً من 12 ظهراً إلى 12 منتصف الليل\nOpen daily: 12 PM - 12 AM";
    
    with (hoursFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.contactFont);
        pointSize = textStyles.contactSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "RestaurantHours", colors.accent);
    }
    
    // معلومات الاتصال
    createContactSection(doc, page, colors, 23, 27);
}

function createBusinessFlyer(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes.business;

    createFlyerBackground(doc, page, colors);

    // اسم الشركة
    var companyName = page.textFrames.add();
    companyName.geometricBounds = [2, 2, 6, 19];
    companyName.contents = "شركة الأعمال المتقدمة\nAdvanced Business Solutions";

    with (companyName.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = "28pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "BusinessName", colors.primary);
    }

    // الخدمات
    var servicesFrame = page.textFrames.add();
    servicesFrame.geometricBounds = [7, 2, 18, 19];
    servicesFrame.contents = "خدماتنا - Our Services\n\n" +
                            "✓ الاستشارات التجارية - Business Consulting\n" +
                            "✓ التسويق الرقمي - Digital Marketing\n" +
                            "✓ تطوير المواقع - Web Development\n" +
                            "✓ إدارة المشاريع - Project Management\n" +
                            "✓ التدريب المهني - Professional Training\n\n" +
                            "احصل على استشارة مجانية اليوم!\nGet a free consultation today!";

    with (servicesFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "12pt";
        justification = Justification.LEFT_ALIGN;
        fillColor = createColorFromValues(doc, "BusinessServices", colors.text);
    }

    // معلومات الاتصال
    createContactSection(doc, page, colors, 19, 27);
}

function createRealEstateFlyer(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes.realestate;

    createFlyerBackground(doc, page, colors);

    // عنوان العقار
    var propertyTitle = page.textFrames.add();
    propertyTitle.geometricBounds = [2, 2, 6, 19];
    propertyTitle.contents = "عقار للبيع\nProperty for Sale";

    with (propertyTitle.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = "32pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "PropertyTitle", colors.primary);
    }

    // تفاصيل العقار
    var propertyDetails = page.textFrames.add();
    propertyDetails.geometricBounds = [7, 2, 18, 19];
    propertyDetails.contents = "تفاصيل العقار - Property Details\n\n" +
                              "• المساحة: 200 متر مربع - Area: 200 sqm\n" +
                              "• غرف النوم: 3 - Bedrooms: 3\n" +
                              "• دورات المياه: 2 - Bathrooms: 2\n" +
                              "• المطبخ: مجهز بالكامل - Kitchen: Fully equipped\n" +
                              "• موقف السيارات: متوفر - Parking: Available\n" +
                              "• الحديقة: 50 متر مربع - Garden: 50 sqm\n\n" +
                              "السعر: 2,500,000 ج.م\nPrice: 2,500,000 EGP";

    with (propertyDetails.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "11pt";
        justification = Justification.LEFT_ALIGN;
        fillColor = createColorFromValues(doc, "PropertyDetails", colors.text);
    }

    // معلومات الاتصال
    createContactSection(doc, page, colors, 19, 27);
}

// الدوال المساعدة
function createFlyerBackground(doc, page, colors) {
    var bg = page.rectangles.add();
    bg.geometricBounds = page.bounds;
    bg.fillColor = createColorFromValues(doc, "Background", colors.secondary);
    bg.strokeColor = doc.colors.item("None");
    bg.sendToBack();

    if (designSettings.backgroundPattern) {
        // إضافة نمط خلفية بسيط
        for (var i = 0; i < 5; i++) {
            var pattern = page.ovals.add();
            pattern.geometricBounds = [
                Math.random() * 25 + 2,
                Math.random() * 15 + 2,
                Math.random() * 25 + 4,
                Math.random() * 15 + 4
            ];
            pattern.fillColor = createColorFromValues(doc, "Pattern" + i, colors.secondary);
            pattern.strokeColor = doc.colors.item("None");
            pattern.transparencySettings.blendingSettings.opacity = 20;
            pattern.sendToBack();
        }
    }
}

function createProductShowcase(doc, page, colors, startY, endY) {
    var showcaseFrame = page.textFrames.add();
    showcaseFrame.geometricBounds = [startY, 2, endY, 19];
    showcaseFrame.contents = "منتجاتنا المميزة - Featured Products\n\n" +
                            "• منتج رقم 1 - خصم 30%\n" +
                            "• منتج رقم 2 - خصم 25%\n" +
                            "• منتج رقم 3 - خصم 40%\n" +
                            "• منتج رقم 4 - خصم 35%\n\n" +
                            "تسوق الآن ولا تفوت الفرصة!\nShop now and don't miss out!";

    with (showcaseFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = textStyles.bodySize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "Showcase", colors.text);
    }
}

function createContactSection(doc, page, colors, startY, endY) {
    var contactFrame = page.textFrames.add();
    contactFrame.geometricBounds = [startY, 2, endY, 19];

    var contactText = "تواصل معنا - Contact Us\n\n";
    contactText += designSettings.contactInfo + "\n";
    contactText += designSettings.website + "\n";
    contactText += designSettings.address;

    contactFrame.contents = contactText;

    with (contactFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.contactFont);
        pointSize = textStyles.contactSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "Contact", colors.text);
    }

    // إضافة QR Code placeholder إذا كان مطلوباً
    if (designSettings.includeQR) {
        var qrFrame = page.rectangles.add();
        qrFrame.geometricBounds = [startY + 1, 16, startY + 4, 19];
        qrFrame.fillColor = createColorFromValues(doc, "QRBg", colors.secondary);
        qrFrame.strokeColor = createColorFromValues(doc, "QRBorder", colors.primary);
        qrFrame.strokeWeight = "1pt";

        var qrText = qrFrame.textFrames.add();
        qrText.contents = "QR\nCode";
        with (qrText.parentStory.characters.everyItem()) {
            appliedFont = app.fonts.item(textStyles.contactFont);
            pointSize = "8pt";
            justification = Justification.CENTER_ALIGN;
            fillColor = createColorFromValues(doc, "QRText", colors.text);
        }
    }
}

function addDecorativeBorder(doc, page, colors) {
    var border = page.rectangles.add();
    border.geometricBounds = [0.5, 0.5, parseFloat(flyerSettings.height) - 0.5, parseFloat(flyerSettings.width) - 0.5];
    border.fillColor = doc.colors.item("None");
    border.strokeColor = createColorFromValues(doc, "Border", colors.primary);
    border.strokeWeight = "3pt";
}

function createColorFromValues(doc, colorName, colorValues) {
    try {
        return doc.colors.item(colorName);
    } catch (e) {
        var color = doc.colors.add();
        color.name = colorName;
        color.model = ColorModel.PROCESS;
        color.colorValue = colorValues;
        return color;
    }
}

// تشغيل السكربت
main();
