// سكربت قوالب السوشيال ميديا الاحترافية
// Professional Social Media Templates Generator for Adobe InDesign
// المطور: Augment Agent
// التاريخ: 2025-08-30

// أحجام منصات التواصل الاجتماعي
var socialMediaSizes = {
    // Instagram
    INSTAGRAM_POST: {name: "Instagram Post", width: "1080px", height: "1080px"},
    INSTAGRAM_STORY: {name: "Instagram Story", width: "1080px", height: "1920px"},
    INSTAGRAM_REEL: {name: "Instagram Reel", width: "1080px", height: "1920px"},
    
    // Facebook
    FACEBOOK_POST: {name: "Facebook Post", width: "1200px", height: "630px"},
    FACEBOOK_COVER: {name: "Facebook Cover", width: "1640px", height: "859px"},
    FACEBOOK_STORY: {name: "Facebook Story", width: "1080px", height: "1920px"},
    
    // Twitter/X
    TWITTER_POST: {name: "Twitter Post", width: "1200px", height: "675px"},
    TWITTER_HEADER: {name: "Twitter Header", width: "1500px", height: "500px"},
    
    // LinkedIn
    LINKEDIN_POST: {name: "LinkedIn Post", width: "1200px", height: "627px"},
    LINKEDIN_COVER: {name: "LinkedIn Cover", width: "1584px", height: "396px"},
    
    // YouTube
    YOUTUBE_THUMBNAIL: {name: "YouTube Thumbnail", width: "1280px", height: "720px"},
    YOUTUBE_BANNER: {name: "YouTube Banner", width: "2560px", height: "1440px"},
    
    // TikTok
    TIKTOK_VIDEO: {name: "TikTok Video", width: "1080px", height: "1920px"}
};

// أنواع القوالب
var templateTypes = {
    PRODUCT: "product",         // منتج
    QUOTE: "quote",            // اقتباس
    ANNOUNCEMENT: "announcement", // إعلان
    STORY: "story",            // قصة
    PROMOTION: "promotion"      // ترويج
};

// إعدادات التصميم
var designSettings = {
    templateType: templateTypes.PRODUCT,
    socialPlatform: "INSTAGRAM_POST",
    mainText: "النص الرئيسي",
    subText: "النص الفرعي",
    brandName: "اسم العلامة التجارية",
    hashtags: "#تصميم #سوشيال_ميديا #إبداع",
    includelogo: true,
    includeBranding: true,
    useGradient: true,
    modernStyle: true
};

// مخططات الألوان العصرية
var colorSchemes = {
    vibrant: {
        primary: [0, 80, 100, 0],      // أحمر نابض
        secondary: [100, 0, 80, 0],    // أزرق نابض
        accent: [0, 0, 100, 0],        // أصفر
        background: [0, 5, 15, 0],     // خلفية فاتحة
        text: [0, 0, 0, 100]           // نص أسود
    },
    minimal: {
        primary: [0, 0, 0, 100],       // أسود
        secondary: [0, 0, 0, 20],      // رمادي فاتح
        accent: [0, 100, 100, 0],      // أحمر للتأكيد
        background: [0, 0, 0, 0],      // أبيض
        text: [0, 0, 0, 80]            // رمادي داكن
    },
    sunset: {
        primary: [0, 70, 100, 0],      // برتقالي
        secondary: [0, 30, 80, 0],     // برتقالي فاتح
        accent: [0, 100, 100, 10],     // أحمر داكن
        background: [0, 10, 30, 0],    // كريمي
        text: [0, 0, 0, 100]           // أسود
    },
    ocean: {
        primary: [100, 50, 0, 0],      // أزرق
        secondary: [80, 30, 0, 0],     // أزرق فاتح
        accent: [0, 80, 100, 0],       // برتقالي
        background: [50, 10, 0, 0],    // أزرق فاتح جداً
        text: [100, 50, 0, 20]         // أزرق داكن
    },
    nature: {
        primary: [50, 0, 100, 0],      // أخضر
        secondary: [30, 0, 80, 0],     // أخضر فاتح
        accent: [0, 50, 100, 0],       // برتقالي
        background: [20, 0, 40, 0],    // أخضر فاتح جداً
        text: [0, 0, 0, 100]           // أسود
    }
};

// إعدادات النصوص العصرية
var textStyles = {
    titleFont: "Arial-BoldMT",
    titleSize: "48pt",
    subtitleFont: "Arial",
    subtitleSize: "24pt",
    bodyFont: "Arial",
    bodySize: "18pt",
    brandFont: "Arial-BoldMT",
    brandSize: "20pt",
    hashtagFont: "Arial",
    hashtagSize: "14pt"
};

function main() {
    try {
        // عرض نافذة اختيار القالب
        var settings = showTemplateDialog();
        if (!settings) return;
        
        // تطبيق الإعدادات المختارة
        Object.assign(designSettings, settings);
        
        // إنشاء القالب
        var doc = createSocialMediaDocument();
        
        switch (designSettings.templateType) {
            case templateTypes.PRODUCT:
                createProductTemplate(doc);
                break;
            case templateTypes.QUOTE:
                createQuoteTemplate(doc);
                break;
            case templateTypes.ANNOUNCEMENT:
                createAnnouncementTemplate(doc);
                break;
            case templateTypes.STORY:
                createStoryTemplate(doc);
                break;
            case templateTypes.PROMOTION:
                createPromotionTemplate(doc);
                break;
        }
        
        alert("تم إنشاء قالب السوشيال ميديا بنجاح!\nSocial media template created successfully!");
        
    } catch (error) {
        alert("خطأ: " + error.message + "\nError: " + error.message);
    }
}

function showTemplateDialog() {
    var dialog = app.dialogs.add({name: "إعدادات قالب السوشيال ميديا"});
    
    with (dialog.dialogColumns.add()) {
        // اختيار المنصة
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "اختر المنصة:"});
        }
        var platformDropdown = dropdowns.add({stringList: [
            "Instagram Post", "Instagram Story", "Facebook Post", 
            "Twitter Post", "LinkedIn Post", "YouTube Thumbnail"
        ]});
        
        // اختيار نوع القالب
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "نوع القالب:"});
        }
        var typeDropdown = dropdowns.add({stringList: [
            "منتج - Product", "اقتباس - Quote", "إعلان - Announcement", 
            "قصة - Story", "ترويج - Promotion"
        ]});
        
        // اختيار مخطط الألوان
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "مخطط الألوان:"});
        }
        var colorDropdown = dropdowns.add({stringList: [
            "نابض - Vibrant", "بسيط - Minimal", "غروب - Sunset", 
            "محيط - Ocean", "طبيعة - Nature"
        ]});
        
        // النص الرئيسي
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "النص الرئيسي:"});
        }
        var mainTextInput = textEditboxes.add({editContents: "النص الرئيسي"});
        
        // النص الفرعي
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "النص الفرعي:"});
        }
        var subTextInput = textEditboxes.add({editContents: "النص الفرعي"});
    }
    
    var result = dialog.show();
    if (result) {
        var platforms = ["INSTAGRAM_POST", "INSTAGRAM_STORY", "FACEBOOK_POST", 
                        "TWITTER_POST", "LINKEDIN_POST", "YOUTUBE_THUMBNAIL"];
        var types = [templateTypes.PRODUCT, templateTypes.QUOTE, templateTypes.ANNOUNCEMENT, 
                    templateTypes.STORY, templateTypes.PROMOTION];
        var colors = ["vibrant", "minimal", "sunset", "ocean", "nature"];
        
        var settings = {
            socialPlatform: platforms[platformDropdown.selectedIndex],
            templateType: types[typeDropdown.selectedIndex],
            colorScheme: colors[colorDropdown.selectedIndex],
            mainText: mainTextInput.editContents,
            subText: subTextInput.editContents
        };
        
        dialog.destroy();
        return settings;
    }
    
    dialog.destroy();
    return null;
}

function createSocialMediaDocument() {
    var doc = app.documents.add();
    var size = socialMediaSizes[designSettings.socialPlatform];
    
    with (doc.documentPreferences) {
        pageWidth = size.width;
        pageHeight = size.height;
        facingPages = false;
        pagesPerDocument = 1;
    }
    
    // بدون هوامش للسوشيال ميديا
    with (doc.pages[0].marginPreferences) {
        top = "0mm";
        bottom = "0mm";
        left = "0mm";
        right = "0mm";
    }
    
    return doc;
}

function createProductTemplate(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes[designSettings.colorScheme || "vibrant"];
    var size = socialMediaSizes[designSettings.socialPlatform];
    var isSquare = parseFloat(size.width) === parseFloat(size.height);
    var isVertical = parseFloat(size.height) > parseFloat(size.width);
    
    // خلفية متدرجة
    createGradientBackground(doc, page, colors);
    
    // منطقة الصورة (النصف العلوي أو الجانب الأيسر)
    var imageArea = page.rectangles.add();
    if (isVertical) {
        // تخطيط عمودي
        imageArea.geometricBounds = [0, 0, parseFloat(size.height) * 0.6, parseFloat(size.width)];
    } else {
        // تخطيط أفقي أو مربع
        imageArea.geometricBounds = [0, 0, parseFloat(size.height), parseFloat(size.width) * 0.6];
    }
    
    imageArea.fillColor = createColorFromValues(doc, "ImageBg", colors.secondary);
    imageArea.strokeColor = doc.colors.item("None");
    
    // نص الصورة
    var imageText = imageArea.textFrames.add();
    imageText.contents = "صورة المنتج\nProduct Image";
    with (imageText.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = isVertical ? "24pt" : "18pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "ImageText", colors.text);
    }
    
    // النص الرئيسي
    var titleFrame = page.textFrames.add();
    if (isVertical) {
        titleFrame.geometricBounds = [
            parseFloat(size.height) * 0.62,
            parseFloat(size.width) * 0.05,
            parseFloat(size.height) * 0.8,
            parseFloat(size.width) * 0.95
        ];
    } else {
        titleFrame.geometricBounds = [
            parseFloat(size.height) * 0.1,
            parseFloat(size.width) * 0.62,
            parseFloat(size.height) * 0.5,
            parseFloat(size.width) * 0.98
        ];
    }
    
    titleFrame.contents = designSettings.mainText;
    with (titleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = isVertical ? textStyles.titleSize : "36pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "Title", colors.primary);
    }
    
    // النص الفرعي
    var subtitleFrame = page.textFrames.add();
    if (isVertical) {
        subtitleFrame.geometricBounds = [
            parseFloat(size.height) * 0.82,
            parseFloat(size.width) * 0.05,
            parseFloat(size.height) * 0.92,
            parseFloat(size.width) * 0.95
        ];
    } else {
        subtitleFrame.geometricBounds = [
            parseFloat(size.height) * 0.52,
            parseFloat(size.width) * 0.62,
            parseFloat(size.height) * 0.75,
            parseFloat(size.width) * 0.98
        ];
    }
    
    subtitleFrame.contents = designSettings.subText;
    with (subtitleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.subtitleFont);
        pointSize = isVertical ? textStyles.subtitleSize : "20pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "Subtitle", colors.text);
    }
    
    // العلامة التجارية والهاشتاغ
    addBrandingElements(doc, page, colors, isVertical);
}

function createQuoteTemplate(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes[designSettings.colorScheme || "minimal"];
    var size = socialMediaSizes[designSettings.socialPlatform];

    // خلفية بسيطة
    createSolidBackground(doc, page, colors.background);

    // إطار الاقتباس
    var quoteFrame = page.rectangles.add();
    quoteFrame.geometricBounds = [
        parseFloat(size.height) * 0.2,
        parseFloat(size.width) * 0.1,
        parseFloat(size.height) * 0.7,
        parseFloat(size.width) * 0.9
    ];
    quoteFrame.fillColor = createColorFromValues(doc, "QuoteBg", colors.secondary);
    quoteFrame.strokeColor = createColorFromValues(doc, "QuoteBorder", colors.primary);
    quoteFrame.strokeWeight = "3pt";

    // نص الاقتباس
    var quoteText = quoteFrame.textFrames.add();
    quoteText.contents = '"' + designSettings.mainText + '"';
    with (quoteText.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = "32pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "QuoteText", colors.primary);
    }

    // مصدر الاقتباس
    var authorFrame = page.textFrames.add();
    authorFrame.geometricBounds = [
        parseFloat(size.height) * 0.75,
        parseFloat(size.width) * 0.1,
        parseFloat(size.height) * 0.85,
        parseFloat(size.width) * 0.9
    ];
    authorFrame.contents = "- " + designSettings.subText;
    with (authorFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.subtitleFont);
        pointSize = "18pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "Author", colors.text);
    }

    addBrandingElements(doc, page, colors, true);
}

function createAnnouncementTemplate(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes[designSettings.colorScheme || "vibrant"];
    var size = socialMediaSizes[designSettings.socialPlatform];

    // خلفية متدرجة قوية
    createGradientBackground(doc, page, colors);

    // شارة "إعلان مهم"
    var badge = page.ovals.add();
    badge.geometricBounds = [
        parseFloat(size.height) * 0.05,
        parseFloat(size.width) * 0.7,
        parseFloat(size.height) * 0.2,
        parseFloat(size.width) * 0.95
    ];
    badge.fillColor = createColorFromValues(doc, "Badge", colors.accent);
    badge.strokeColor = doc.colors.item("None");

    var badgeText = badge.textFrames.add();
    badgeText.contents = "إعلان\nمهم";
    with (badgeText.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.brandFont);
        pointSize = "14pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }

    // العنوان الرئيسي
    var titleFrame = page.textFrames.add();
    titleFrame.geometricBounds = [
        parseFloat(size.height) * 0.25,
        parseFloat(size.width) * 0.05,
        parseFloat(size.height) * 0.55,
        parseFloat(size.width) * 0.95
    ];
    titleFrame.contents = designSettings.mainText;
    with (titleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = "40pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }

    // التفاصيل
    var detailsFrame = page.textFrames.add();
    detailsFrame.geometricBounds = [
        parseFloat(size.height) * 0.6,
        parseFloat(size.width) * 0.05,
        parseFloat(size.height) * 0.8,
        parseFloat(size.width) * 0.95
    ];
    detailsFrame.contents = designSettings.subText;
    with (detailsFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "20pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }

    addBrandingElements(doc, page, colors, true);
}

function createStoryTemplate(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes[designSettings.colorScheme || "sunset"];
    var size = socialMediaSizes[designSettings.socialPlatform];

    // خلفية Story عمودية
    createGradientBackground(doc, page, colors);

    // منطقة المحتوى الرئيسي
    var contentArea = page.rectangles.add();
    contentArea.geometricBounds = [
        parseFloat(size.height) * 0.15,
        parseFloat(size.width) * 0.05,
        parseFloat(size.height) * 0.75,
        parseFloat(size.width) * 0.95
    ];
    contentArea.fillColor = doc.colors.item("Paper");
    contentArea.strokeColor = doc.colors.item("None");
    contentArea.transparencySettings.blendingSettings.opacity = 90;

    // العنوان
    var titleFrame = page.textFrames.add();
    titleFrame.geometricBounds = [
        parseFloat(size.height) * 0.2,
        parseFloat(size.width) * 0.1,
        parseFloat(size.height) * 0.4,
        parseFloat(size.width) * 0.9
    ];
    titleFrame.contents = designSettings.mainText;
    with (titleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = "36pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "StoryTitle", colors.primary);
    }

    // المحتوى
    var contentFrame = page.textFrames.add();
    contentFrame.geometricBounds = [
        parseFloat(size.height) * 0.45,
        parseFloat(size.width) * 0.1,
        parseFloat(size.height) * 0.7,
        parseFloat(size.width) * 0.9
    ];
    contentFrame.contents = designSettings.subText;
    with (contentFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "22pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "StoryContent", colors.text);
    }

    addBrandingElements(doc, page, colors, true);
}

function createPromotionTemplate(doc) {
    var page = doc.pages[0];
    var colors = colorSchemes[designSettings.colorScheme || "ocean"];
    var size = socialMediaSizes[designSettings.socialPlatform];

    // خلفية ترويجية جذابة
    createGradientBackground(doc, page, colors);

    // شارة خصم كبيرة
    var discountBadge = page.ovals.add();
    var badgeSize = Math.min(parseFloat(size.width), parseFloat(size.height)) * 0.3;
    discountBadge.geometricBounds = [
        parseFloat(size.height) * 0.1,
        parseFloat(size.width) * 0.1,
        parseFloat(size.height) * 0.1 + badgeSize,
        parseFloat(size.width) * 0.1 + badgeSize
    ];
    discountBadge.fillColor = createColorFromValues(doc, "DiscountBg", colors.accent);
    discountBadge.strokeColor = doc.colors.item("Paper");
    discountBadge.strokeWeight = "4pt";

    var discountText = discountBadge.textFrames.add();
    discountText.contents = "خصم\n50%\nOFF";
    with (discountText.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.titleFont);
        pointSize = "24pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }

    // النص الترويجي
    var promoFrame = page.textFrames.add();
    promoFrame.geometricBounds = [
        parseFloat(size.height) * 0.5,
        parseFloat(size.width) * 0.05,
        parseFloat(size.height) * 0.8,
        parseFloat(size.width) * 0.95
    ];
    promoFrame.contents = designSettings.mainText + "\n\n" + designSettings.subText;
    with (promoFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "26pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }

    addBrandingElements(doc, page, colors, true);
}

// الدوال المساعدة
function createGradientBackground(doc, page, colors) {
    try {
        var bg = page.rectangles.add();
        bg.geometricBounds = page.bounds;

        var gradient = doc.gradients.add();
        gradient.name = "SocialGradient_" + Math.random();
        gradient.type = GradientType.LINEAR;

        var color1 = createColorFromValues(doc, "GradStart_" + Math.random(), colors.primary);
        var color2 = createColorFromValues(doc, "GradEnd_" + Math.random(), colors.secondary);

        gradient.gradientStops[0].stopColor = color1;
        gradient.gradientStops[1].stopColor = color2;

        bg.fillColor = gradient;
        bg.strokeColor = doc.colors.item("None");
        bg.sendToBack();

    } catch (e) {
        createSolidBackground(doc, page, colors.primary);
    }
}

function createSolidBackground(doc, page, colorValues) {
    var bg = page.rectangles.add();
    bg.geometricBounds = page.bounds;
    bg.fillColor = createColorFromValues(doc, "SolidBg", colorValues);
    bg.strokeColor = doc.colors.item("None");
    bg.sendToBack();
}

function addBrandingElements(doc, page, colors, isVertical) {
    var size = socialMediaSizes[designSettings.socialPlatform];

    // اسم العلامة التجارية
    if (designSettings.includeBranding) {
        var brandFrame = page.textFrames.add();
        brandFrame.geometricBounds = [
            parseFloat(size.height) * 0.9,
            parseFloat(size.width) * 0.05,
            parseFloat(size.height) * 0.95,
            parseFloat(size.width) * 0.6
        ];
        brandFrame.contents = designSettings.brandName;
        with (brandFrame.parentStory.characters.everyItem()) {
            appliedFont = app.fonts.item(textStyles.brandFont);
            pointSize = textStyles.brandSize;
            justification = Justification.LEFT_ALIGN;
            fillColor = createColorFromValues(doc, "Brand", colors.text);
        }
    }

    // الهاشتاغات
    var hashtagFrame = page.textFrames.add();
    hashtagFrame.geometricBounds = [
        parseFloat(size.height) * 0.95,
        parseFloat(size.width) * 0.05,
        parseFloat(size.height) * 0.98,
        parseFloat(size.width) * 0.95
    ];
    hashtagFrame.contents = designSettings.hashtags;
    with (hashtagFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.hashtagFont);
        pointSize = textStyles.hashtagSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createColorFromValues(doc, "Hashtag", colors.accent);
    }
}

function createColorFromValues(doc, colorName, colorValues) {
    try {
        return doc.colors.item(colorName);
    } catch (e) {
        var color = doc.colors.add();
        color.name = colorName;
        color.model = ColorModel.PROCESS;
        color.colorValue = colorValues;
        return color;
    }
}

// تشغيل السكربت
main();
