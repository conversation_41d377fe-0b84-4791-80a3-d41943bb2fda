// مجموعة أدوات InDesign الشاملة الاحترافية
// InDesign Master Toolkit - Professional Suite
// المطور: Augment Agent
// التاريخ: 2025-08-30

// قائمة الأدوات المتاحة
var availableTools = {
    PRODUCT_GRID: "product_grid",           // شبكة المنتجات الأساسية
    ENHANCED_GRID: "enhanced_grid",         // شبكة محسنة مع تأثيرات
    CATALOG: "catalog",                     // كتالوج متعدد الصفحات
    FLYER_SALE: "flyer_sale",              // فلاير تخفيضات
    FLYER_EVENT: "flyer_event",            // فلاير فعالية
    FLYER_RESTAURANT: "flyer_restaurant",   // فلاير مطعم
    SOCIAL_INSTAGRAM: "social_instagram",   // قالب إنستغرام
    SOCIAL_FACEBOOK: "social_facebook",     // قالب فيسبوك
    CALENDAR_MONTHLY: "calendar_monthly",   // تقويم شهري
    CALENDAR_YEARLY: "calendar_yearly",     // تقويم سنوي
    EXCEL_IMPORT: "excel_import",           // استيراد من Excel
    EXPORT_TOOLS: "export_tools",           // أدوات التصدير
    BUSINESS_CARD: "business_card",         // بطاقة أعمال
    CERTIFICATE: "certificate",             // شهادة
    LABEL_MAKER: "label_maker"              // صانع الملصقات
};

// إعدادات عامة
var globalSettings = {
    selectedTool: "",
    language: "both",           // arabic, english, both
    colorScheme: "modern",      // classic, modern, vibrant
    outputQuality: "high",      // high, medium, low
    autoSave: true,            // حفظ تلقائي
    showPreview: true          // عرض معاينة
};

function main() {
    try {
        // عرض القائمة الرئيسية
        var selectedTool = showMainMenu();
        if (!selectedTool) return;
        
        globalSettings.selectedTool = selectedTool;
        
        // تشغيل الأداة المختارة
        switch (selectedTool) {
            case availableTools.PRODUCT_GRID:
                runProductGridTool();
                break;
            case availableTools.ENHANCED_GRID:
                runEnhancedGridTool();
                break;
            case availableTools.CATALOG:
                runCatalogTool();
                break;
            case availableTools.FLYER_SALE:
                runFlyerTool("sale");
                break;
            case availableTools.FLYER_EVENT:
                runFlyerTool("event");
                break;
            case availableTools.FLYER_RESTAURANT:
                runFlyerTool("restaurant");
                break;
            case availableTools.SOCIAL_INSTAGRAM:
                runSocialMediaTool("instagram");
                break;
            case availableTools.SOCIAL_FACEBOOK:
                runSocialMediaTool("facebook");
                break;
            case availableTools.CALENDAR_MONTHLY:
                runCalendarTool("monthly");
                break;
            case availableTools.CALENDAR_YEARLY:
                runCalendarTool("yearly");
                break;
            case availableTools.EXCEL_IMPORT:
                runExcelImportTool();
                break;
            case availableTools.EXPORT_TOOLS:
                runExportTool();
                break;
            case availableTools.BUSINESS_CARD:
                runBusinessCardTool();
                break;
            case availableTools.CERTIFICATE:
                runCertificateTool();
                break;
            case availableTools.LABEL_MAKER:
                runLabelMakerTool();
                break;
        }
        
    } catch (error) {
        alert("خطأ: " + error.message + "\nError: " + error.message);
    }
}

function showMainMenu() {
    var dialog = app.dialogs.add({name: "مجموعة أدوات InDesign الشاملة"});
    
    with (dialog.dialogColumns.add()) {
        // عنوان
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "اختر الأداة المطلوبة:"});
        }
        
        // قائمة الأدوات
        var toolsList = dropdowns.add({stringList: [
            "🛍️ شبكة المنتجات الأساسية - Basic Product Grid",
            "✨ شبكة المنتجات المحسنة - Enhanced Product Grid", 
            "📖 كتالوج المنتجات - Product Catalog",
            "🏷️ فلاير التخفيضات - Sale Flyer",
            "🎉 فلاير الفعاليات - Event Flyer",
            "🍽️ فلاير المطعم - Restaurant Flyer",
            "📱 قالب إنستغرام - Instagram Template",
            "👥 قالب فيسبوك - Facebook Template",
            "📅 التقويم الشهري - Monthly Calendar",
            "🗓️ التقويم السنوي - Yearly Calendar",
            "📊 استيراد من Excel - Excel Import",
            "💾 أدوات التصدير - Export Tools",
            "💼 بطاقة الأعمال - Business Card",
            "🏆 الشهادات - Certificates",
            "🏷️ صانع الملصقات - Label Maker"
        ]});
        
        // إعدادات عامة
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "الإعدادات العامة:"});
        }
        
        with (dialogRows.add()) {
            var settingsGroup = dialogColumns.add();
            
            // اللغة
            staticTexts.add({staticLabel: "اللغة:"});
            var langDropdown = dropdowns.add({stringList: ["عربي", "إنجليزي", "ثنائي"]});
            
            // مخطط الألوان
            staticTexts.add({staticLabel: "الألوان:"});
            var colorDropdown = dropdowns.add({stringList: ["كلاسيكي", "عصري", "نابض"]});
        }
        
        // خيارات إضافية
        with (dialogRows.add()) {
            var optionsGroup = dialogColumns.add();
            var autoSaveCheck = optionsGroup.checkboxControls.add({staticLabel: "حفظ تلقائي", checkedState: true});
            var previewCheck = optionsGroup.checkboxControls.add({staticLabel: "معاينة", checkedState: true});
        }
        
        // معلومات
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "💡 نصيحة: يمكنك تشغيل كل أداة منفصلة من ملفاتها الخاصة"});
        }
    }
    
    var result = dialog.show();
    if (result) {
        var tools = [
            availableTools.PRODUCT_GRID, availableTools.ENHANCED_GRID, availableTools.CATALOG,
            availableTools.FLYER_SALE, availableTools.FLYER_EVENT, availableTools.FLYER_RESTAURANT,
            availableTools.SOCIAL_INSTAGRAM, availableTools.SOCIAL_FACEBOOK,
            availableTools.CALENDAR_MONTHLY, availableTools.CALENDAR_YEARLY,
            availableTools.EXCEL_IMPORT, availableTools.EXPORT_TOOLS,
            availableTools.BUSINESS_CARD, availableTools.CERTIFICATE, availableTools.LABEL_MAKER
        ];
        
        var languages = ["arabic", "english", "both"];
        var colors = ["classic", "modern", "vibrant"];
        
        globalSettings.language = languages[langDropdown.selectedIndex];
        globalSettings.colorScheme = colors[colorDropdown.selectedIndex];
        globalSettings.autoSave = autoSaveCheck.checkedState;
        globalSettings.showPreview = previewCheck.checkedState;
        
        var selectedTool = tools[toolsList.selectedIndex];
        dialog.destroy();
        return selectedTool;
    }
    
    dialog.destroy();
    return null;
}

// دوال تشغيل الأدوات
function runProductGridTool() {
    alert("سيتم تشغيل أداة شبكة المنتجات الأساسية...\nLaunching Basic Product Grid Tool...");
    // هنا يمكن استدعاء السكربت الأساسي أو تضمين الكود
}

function runEnhancedGridTool() {
    alert("سيتم تشغيل أداة شبكة المنتجات المحسنة...\nLaunching Enhanced Product Grid Tool...");
    // هنا يمكن استدعاء السكربت المحسن
}

function runCatalogTool() {
    alert("سيتم تشغيل أداة كتالوج المنتجات...\nLaunching Product Catalog Tool...");
    // هنا يمكن استدعاء سكربت الكتالوج
}

function runFlyerTool(type) {
    alert("سيتم تشغيل أداة الفلاير (" + type + ")...\nLaunching Flyer Tool (" + type + ")...");
    // هنا يمكن استدعاء سكربت الفلاير
}

function runSocialMediaTool(platform) {
    alert("سيتم تشغيل أداة السوشيال ميديا (" + platform + ")...\nLaunching Social Media Tool (" + platform + ")...");
    // هنا يمكن استدعاء سكربت السوشيال ميديا
}

function runCalendarTool(type) {
    alert("سيتم تشغيل أداة التقويم (" + type + ")...\nLaunching Calendar Tool (" + type + ")...");
    // هنا يمكن استدعاء سكربت التقويم
}

function runExcelImportTool() {
    alert("سيتم تشغيل أداة استيراد Excel...\nLaunching Excel Import Tool...");
    // هنا يمكن استدعاء سكربت استيراد Excel
}

function runExportTool() {
    alert("سيتم تشغيل أدوات التصدير المتقدمة...\nLaunching Advanced Export Tools...");
    // هنا يمكن استدعاء سكربت التصدير
}

function runBusinessCardTool() {
    // أداة بطاقة الأعمال السريعة
    var doc = app.documents.add();
    
    with (doc.documentPreferences) {
        pageWidth = "9cm";
        pageHeight = "5cm";
        facingPages = false;
        pagesPerDocument = 1;
    }
    
    var page = doc.pages[0];
    
    // خلفية البطاقة
    var bg = page.rectangles.add();
    bg.geometricBounds = page.bounds;
    bg.fillColor = createBusinessCardColor(doc, "Background", [0, 20, 40, 0]);
    bg.strokeColor = doc.colors.item("None");
    
    // اسم الشخص
    var nameFrame = page.textFrames.add();
    nameFrame.geometricBounds = [0.5, 0.5, 1.5, 8.5];
    nameFrame.contents = "الاسم الكامل\nFull Name";
    with (nameFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial-BoldMT");
        pointSize = "16pt";
        justification = Justification.LEFT_ALIGN;
        fillColor = createBusinessCardColor(doc, "Name", [0, 0, 0, 100]);
    }
    
    // المنصب
    var titleFrame = page.textFrames.add();
    titleFrame.geometricBounds = [1.5, 0.5, 2.2, 8.5];
    titleFrame.contents = "المنصب - Job Title";
    with (titleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial");
        pointSize = "12pt";
        justification = Justification.LEFT_ALIGN;
        fillColor = createBusinessCardColor(doc, "Title", [0, 50, 100, 0]);
    }
    
    // معلومات الاتصال
    var contactFrame = page.textFrames.add();
    contactFrame.geometricBounds = [2.5, 0.5, 4.5, 8.5];
    contactFrame.contents = "📞 +20 123 456 7890\n✉️ <EMAIL>\n🌐 www.company.com\n📍 العنوان، المدينة";
    with (contactFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial");
        pointSize = "10pt";
        justification = Justification.LEFT_ALIGN;
        fillColor = createBusinessCardColor(doc, "Contact", [0, 0, 0, 80]);
    }
    
    alert("تم إنشاء بطاقة الأعمال!\nBusiness card created!");
}

function runCertificateTool() {
    // أداة الشهادات السريعة
    var doc = app.documents.add();
    
    with (doc.documentPreferences) {
        pageWidth = "29.7cm";
        pageHeight = "21cm";
        facingPages = false;
        pagesPerDocument = 1;
    }
    
    var page = doc.pages[0];
    
    // إطار الشهادة
    var certFrame = page.rectangles.add();
    certFrame.geometricBounds = [1, 1, 20, 28.7];
    certFrame.strokeWeight = "3pt";
    certFrame.strokeColor = createCertificateColor(doc, "Border", [0, 80, 100, 0]);
    certFrame.fillColor = createCertificateColor(doc, "Background", [0, 5, 15, 0]);
    
    // عنوان الشهادة
    var titleFrame = page.textFrames.add();
    titleFrame.geometricBounds = [3, 2, 6, 27.7];
    titleFrame.contents = "شهادة تقدير\nCertificate of Appreciation";
    with (titleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial-BoldMT");
        pointSize = "32pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createCertificateColor(doc, "Title", [0, 80, 100, 0]);
    }
    
    // نص الشهادة
    var certText = page.textFrames.add();
    certText.geometricBounds = [8, 2, 15, 27.7];
    certText.contents = "تُمنح هذه الشهادة إلى\nThis certificate is awarded to\n\n" +
                       "________________________\n\n" +
                       "تقديراً لجهوده المتميزة\nIn recognition of outstanding efforts\n\n" +
                       "التاريخ: ___________\nDate: ___________";
    with (certText.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item("Arial");
        pointSize = "16pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createCertificateColor(doc, "Text", [0, 0, 0, 100]);
    }
    
    alert("تم إنشاء الشهادة!\nCertificate created!");
}

function runLabelMakerTool() {
    // أداة صانع الملصقات
    var doc = app.documents.add();
    
    with (doc.documentPreferences) {
        pageWidth = "21cm";
        pageHeight = "29.7cm";
        facingPages = false;
        pagesPerDocument = 1;
    }
    
    var page = doc.pages[0];
    
    // شبكة ملصقات (4x8 = 32 ملصق)
    var labelWidth = 5;
    var labelHeight = 3.5;
    var marginX = 0.5;
    var marginY = 0.5;
    
    for (var row = 0; row < 8; row++) {
        for (var col = 0; col < 4; col++) {
            var labelX = marginX + col * labelWidth;
            var labelY = marginY + row * labelHeight;
            
            // إطار الملصق
            var label = page.rectangles.add();
            label.geometricBounds = [labelY, labelX, labelY + labelHeight - 0.1, labelX + labelWidth - 0.1];
            label.strokeWeight = "0.5pt";
            label.strokeColor = createLabelColor(doc, "LabelBorder", [0, 0, 0, 50]);
            label.fillColor = createLabelColor(doc, "LabelBg", [0, 0, 0, 0]);
            
            // نص الملصق
            var labelText = label.textFrames.add();
            labelText.contents = "ملصق " + (row * 4 + col + 1) + "\nLabel " + (row * 4 + col + 1);
            with (labelText.parentStory.characters.everyItem()) {
                appliedFont = app.fonts.item("Arial");
                pointSize = "10pt";
                justification = Justification.CENTER_ALIGN;
                fillColor = createLabelColor(doc, "LabelText", [0, 0, 0, 100]);
            }
        }
    }
    
    alert("تم إنشاء شبكة الملصقات!\nLabel grid created!");
}

// دوال مساعدة لإنشاء الألوان
function createBusinessCardColor(doc, colorName, colorValues) {
    return createColorFromValues(doc, "BC_" + colorName, colorValues);
}

function createCertificateColor(doc, colorName, colorValues) {
    return createColorFromValues(doc, "CERT_" + colorName, colorValues);
}

function createLabelColor(doc, colorName, colorValues) {
    return createColorFromValues(doc, "LABEL_" + colorName, colorValues);
}

function createColorFromValues(doc, colorName, colorValues) {
    try {
        return doc.colors.item(colorName);
    } catch (e) {
        var color = doc.colors.add();
        color.name = colorName;
        color.model = ColorModel.PROCESS;
        color.colorValue = colorValues;
        return color;
    }
}

// رسالة ترحيب
function showWelcomeMessage() {
    var welcomeDialog = app.dialogs.add({name: "مرحباً بك في مجموعة أدوات InDesign"});
    
    with (welcomeDialog.dialogColumns.add()) {
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "🎨 مجموعة أدوات InDesign الشاملة الاحترافية"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "تم تطويرها بواسطة Augment Agent"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: ""});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "الأدوات المتاحة:"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "• شبكات المنتجات والكتالوجات"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "• الفلايرز والإعلانات"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "• قوالب السوشيال ميديا"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "• التقاويم المخصصة"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "• أدوات الاستيراد والتصدير"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "• بطاقات الأعمال والشهادات"});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: ""});
        }
        with (dialogRows.add()) {
            staticTexts.add({staticLabel: "اختر 'موافق' للمتابعة"});
        }
    }
    
    welcomeDialog.show();
    welcomeDialog.destroy();
}

// عرض رسالة الترحيب ثم القائمة الرئيسية
showWelcomeMessage();

// تشغيل السكربت
main();
