// سكربت InDesign لإنشاء شبكة منتجات احترافية
// Product Grid Script for Adobe InDesign
// المطور: Augment Agent
// التاريخ: 2025-08-30

// إعدادات الصفحة والتخطيط
var pageSettings = {
    width: "34cm",      // عرض الصفحة
    height: "24cm",     // ارتفاع الصفحة
    margins: {
        top: "1cm",
        bottom: "1cm", 
        left: "1cm",
        right: "1cm"
    }
};

// إعدادات الشبكة
var gridSettings = {
    rows: 5,           // عدد الصفوف
    columns: 4,        // عدد الأعمدة
    gutter: "0.5cm"    // المسافة بين المربعات
};

// إعدادات النصوص
var textSettings = {
    productNameFont: "Arial-BoldMT",
    productNameSize: "12pt",
    priceFont: "Arial",
    priceSizeOld: "10pt",
    priceSizeNew: "14pt",
    colorOldPrice: [100, 100, 100], // رمادي للسعر القديم
    colorNewPrice: [100, 0, 0, 0]   // أحمر للسعر الجديد
};

function main() {
    try {
        // التحقق من وجود مستند مفتوح
        if (app.documents.length == 0) {
            createNewDocument();
        }
        
        var doc = app.activeDocument;
        
        // إنشاء الشبكة
        createProductGrid(doc);
        
        alert("تم إنشاء الشبكة بنجاح!\nGrid created successfully!");
        
    } catch (error) {
        alert("خطأ: " + error.message + "\nError: " + error.message);
    }
}

function createNewDocument() {
    // إنشاء مستند جديد
    var doc = app.documents.add();
    
    // تعيين إعدادات الصفحة
    with (doc.documentPreferences) {
        pageWidth = pageSettings.width;
        pageHeight = pageSettings.height;
        facingPages = false;
        pagesPerDocument = 1;
    }
    
    // تعيين الهوامش
    with (doc.pages[0].marginPreferences) {
        top = pageSettings.margins.top;
        bottom = pageSettings.margins.bottom;
        left = pageSettings.margins.left;
        right = pageSettings.margins.right;
    }
    
    return doc;
}

function createProductGrid(doc) {
    var page = doc.pages[0];
    var bounds = page.bounds;
    
    // حساب المساحة المتاحة
    var availableWidth = parseFloat(pageSettings.width) - parseFloat(pageSettings.margins.left) - parseFloat(pageSettings.margins.right);
    var availableHeight = parseFloat(pageSettings.height) - parseFloat(pageSettings.margins.top) - parseFloat(pageSettings.margins.bottom);
    
    // حساب أبعاد كل مربع
    var gutterSize = parseFloat(gridSettings.gutter);
    var cellWidth = (availableWidth - (gridSettings.columns - 1) * gutterSize) / gridSettings.columns;
    var cellHeight = (availableHeight - (gridSettings.rows - 1) * gutterSize) / gridSettings.rows;
    
    // إنشاء المربعات
    for (var row = 0; row < gridSettings.rows; row++) {
        for (var col = 0; col < gridSettings.columns; col++) {
            createProductCell(doc, page, row, col, cellWidth, cellHeight, gutterSize);
        }
    }
}

function createProductCell(doc, page, row, col, cellWidth, cellHeight, gutterSize) {
    // حساب موقع المربع
    var startX = parseFloat(pageSettings.margins.left) + col * (cellWidth + gutterSize);
    var startY = parseFloat(pageSettings.margins.top) + row * (cellHeight + gutterSize);
    
    // إنشاء إطار الصورة (60% من ارتفاع المربع)
    var imageHeight = cellHeight * 0.6;
    var imageFrame = page.rectangles.add();
    imageFrame.geometricBounds = [startY, startX, startY + imageHeight, startX + cellWidth];
    
    // تنسيق إطار الصورة
    with (imageFrame) {
        strokeWeight = "0.5pt";
        strokeColor = doc.colors.item("Black");
        fillColor = doc.colors.item("Paper");
        
        // إضافة نص توضيحي للصورة
        var imageText = textFrames.add();
        imageText.geometricBounds = [
            startY + imageHeight/2 - 0.5,
            startX + cellWidth/2 - 1,
            startY + imageHeight/2 + 0.5,
            startX + cellWidth/2 + 1
        ];
        imageText.contents = "صورة المنتج\nProduct Image";
        with (imageText.parentStory.characters.everyItem()) {
            appliedFont = app.fonts.item("Arial");
            pointSize = "8pt";
            justification = Justification.CENTER_ALIGN;
            fillColor = doc.colors.item("Black");
        }
    }
    
    // المساحة المتبقية للنصوص
    var textAreaTop = startY + imageHeight + 0.1;
    var textAreaHeight = cellHeight - imageHeight - 0.1;
    var lineHeight = textAreaHeight / 3;
    
    // إطار اسم المنتج
    var nameFrame = page.textFrames.add();
    nameFrame.geometricBounds = [
        textAreaTop,
        startX,
        textAreaTop + lineHeight,
        startX + cellWidth
    ];
    nameFrame.contents = "اسم المنتج";
    formatText(nameFrame, textSettings.productNameFont, textSettings.productNameSize, Justification.CENTER_ALIGN);
    
    // إطار السعر القديم
    var oldPriceFrame = page.textFrames.add();
    oldPriceFrame.geometricBounds = [
        textAreaTop + lineHeight,
        startX,
        textAreaTop + lineHeight * 2,
        startX + cellWidth
    ];
    oldPriceFrame.contents = "السعر قبل: 000 ج.م";
    formatText(oldPriceFrame, textSettings.priceFont, textSettings.priceSizeOld, Justification.CENTER_ALIGN);
    
    // تطبيق تأثير الشطب على السعر القديم
    with (oldPriceFrame.parentStory.characters.everyItem()) {
        strikeThru = true;
        fillColor = createGrayColor(doc);
    }
    
    // إطار السعر الجديد
    var newPriceFrame = page.textFrames.add();
    newPriceFrame.geometricBounds = [
        textAreaTop + lineHeight * 2,
        startX,
        textAreaTop + lineHeight * 3,
        startX + cellWidth
    ];
    newPriceFrame.contents = "السعر بعد: 000 ج.م";
    formatText(newPriceFrame, textSettings.priceFont, textSettings.priceSizeNew, Justification.CENTER_ALIGN);
    
    // تطبيق اللون الأحمر على السعر الجديد
    with (newPriceFrame.parentStory.characters.everyItem()) {
        fillColor = createRedColor(doc);
    }
    
    // إضافة حدود للمربع الكامل
    var cellBorder = page.rectangles.add();
    cellBorder.geometricBounds = [startY, startX, startY + cellHeight, startX + cellWidth];
    with (cellBorder) {
        strokeWeight = "1pt";
        strokeColor = doc.colors.item("Black");
        fillColor = doc.colors.item("None");
    }
}

function formatText(textFrame, fontName, fontSize, alignment) {
    with (textFrame.parentStory.characters.everyItem()) {
        try {
            appliedFont = app.fonts.item(fontName);
        } catch (e) {
            appliedFont = app.fonts.item("Arial"); // خط احتياطي
        }
        pointSize = fontSize;
        justification = alignment;
    }
}

function createGrayColor(doc) {
    try {
        return doc.colors.item("Gray 60%");
    } catch (e) {
        var grayColor = doc.colors.add();
        grayColor.name = "Gray 60%";
        grayColor.model = ColorModel.PROCESS;
        grayColor.colorValue = [0, 0, 0, 60];
        return grayColor;
    }
}

function createRedColor(doc) {
    try {
        return doc.colors.item("Red");
    } catch (e) {
        var redColor = doc.colors.add();
        redColor.name = "Red";
        redColor.model = ColorModel.PROCESS;
        redColor.colorValue = [0, 100, 100, 0];
        return redColor;
    }
}

// تشغيل السكربت
main();
