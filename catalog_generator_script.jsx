// سكربت إنشاء كتالوج المنتجات الاحترافي
// Professional Product Catalog Generator for Adobe InDesign
// المطور: Augment Agent
// التاريخ: 2025-08-30

// إعدادات الكتالوج
var catalogSettings = {
    pageWidth: "21cm",          // عرض الصفحة A4
    pageHeight: "29.7cm",       // ارتفاع الصفحة A4
    margins: {
        top: "2cm",
        bottom: "2cm",
        left: "1.5cm",
        right: "1.5cm"
    },
    numberOfPages: 20,          // عدد الصفحات
    productsPerPage: 6,         // عدد المنتجات في الصفحة
    includeIndex: true,         // تضمين فهرس
    includeCover: true,         // تضمين غلاف
    includeBackCover: true      // تضمين غلاف خلفي
};

// إعدادات التصميم
var designSettings = {
    coverTitle: "كتالوج المنتجات 2025",
    coverSubtitle: "Product Catalog 2025",
    companyName: "اسم الشركة",
    companyLogo: "ضع شعار الشركة هنا",
    headerHeight: "3cm",
    footerHeight: "1.5cm",
    pageNumbering: true
};

// إعدادات النصوص
var textStyles = {
    coverTitleFont: "Arial-BoldMT",
    coverTitleSize: "36pt",
    headerFont: "Arial-BoldMT", 
    headerSize: "18pt",
    productNameFont: "Arial-BoldMT",
    productNameSize: "14pt",
    bodyFont: "Arial",
    bodySize: "11pt",
    indexFont: "Arial",
    indexSize: "10pt"
};

// ألوان الكتالوج
var catalogColors = {
    primary: [0, 80, 100, 0],      // أزرق
    secondary: [0, 0, 0, 20],      // رمادي فاتح
    accent: [100, 0, 50, 0],       // أحمر
    text: [0, 0, 0, 100]           // أسود
};

function main() {
    try {
        // إنشاء مستند جديد
        var doc = createCatalogDocument();
        
        // إنشاء الغلاف
        if (catalogSettings.includeCover) {
            createCoverPage(doc);
        }
        
        // إنشاء الفهرس
        if (catalogSettings.includeIndex) {
            createIndexPage(doc);
        }
        
        // إنشاء صفحات المنتجات
        createProductPages(doc);
        
        // إنشاء الغلاف الخلفي
        if (catalogSettings.includeBackCover) {
            createBackCover(doc);
        }
        
        // إضافة أرقام الصفحات
        if (designSettings.pageNumbering) {
            addPageNumbers(doc);
        }
        
        alert("تم إنشاء الكتالوج بنجاح!\nCatalog created successfully!");
        
    } catch (error) {
        alert("خطأ: " + error.message + "\nError: " + error.message);
    }
}

function createCatalogDocument() {
    var doc = app.documents.add();
    
    // إعدادات المستند
    with (doc.documentPreferences) {
        pageWidth = catalogSettings.pageWidth;
        pageHeight = catalogSettings.pageHeight;
        facingPages = true;
        pagesPerDocument = catalogSettings.numberOfPages + 
                          (catalogSettings.includeCover ? 1 : 0) +
                          (catalogSettings.includeIndex ? 1 : 0) +
                          (catalogSettings.includeBackCover ? 1 : 0);
    }
    
    // إعدادات الهوامش
    doc.masterSpreads[0].pages.everyItem().marginPreferences.properties = {
        top: catalogSettings.margins.top,
        bottom: catalogSettings.margins.bottom,
        left: catalogSettings.margins.left,
        right: catalogSettings.margins.right
    };
    
    return doc;
}

function createCoverPage(doc) {
    var coverPage = doc.pages[0];
    
    // خلفية الغلاف
    var coverBg = coverPage.rectangles.add();
    coverBg.geometricBounds = coverPage.bounds;
    coverBg.fillColor = createPrimaryColor(doc);
    coverBg.strokeColor = doc.colors.item("None");
    
    // عنوان الكتالوج
    var titleFrame = coverPage.textFrames.add();
    titleFrame.geometricBounds = [
        parseFloat(catalogSettings.pageHeight) * 0.3,
        parseFloat(catalogSettings.margins.left),
        parseFloat(catalogSettings.pageHeight) * 0.5,
        parseFloat(catalogSettings.pageWidth) - parseFloat(catalogSettings.margins.right)
    ];
    titleFrame.contents = designSettings.coverTitle;
    
    with (titleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.coverTitleFont);
        pointSize = textStyles.coverTitleSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }
    
    // العنوان الفرعي
    var subtitleFrame = coverPage.textFrames.add();
    subtitleFrame.geometricBounds = [
        parseFloat(catalogSettings.pageHeight) * 0.5,
        parseFloat(catalogSettings.margins.left),
        parseFloat(catalogSettings.pageHeight) * 0.6,
        parseFloat(catalogSettings.pageWidth) - parseFloat(catalogSettings.margins.right)
    ];
    subtitleFrame.contents = designSettings.coverSubtitle;
    
    with (subtitleFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.headerFont);
        pointSize = "24pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }
    
    // اسم الشركة
    var companyFrame = coverPage.textFrames.add();
    companyFrame.geometricBounds = [
        parseFloat(catalogSettings.pageHeight) * 0.8,
        parseFloat(catalogSettings.margins.left),
        parseFloat(catalogSettings.pageHeight) * 0.9,
        parseFloat(catalogSettings.pageWidth) - parseFloat(catalogSettings.margins.right)
    ];
    companyFrame.contents = designSettings.companyName;
    
    with (companyFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.headerFont);
        pointSize = "18pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }
}

function createIndexPage(doc) {
    var indexPageNum = catalogSettings.includeCover ? 1 : 0;
    var indexPage = doc.pages[indexPageNum];
    
    // عنوان الفهرس
    var indexTitle = indexPage.textFrames.add();
    indexTitle.geometricBounds = [
        parseFloat(catalogSettings.margins.top),
        parseFloat(catalogSettings.margins.left),
        parseFloat(catalogSettings.margins.top) + 1,
        parseFloat(catalogSettings.pageWidth) - parseFloat(catalogSettings.margins.right)
    ];
    indexTitle.contents = "الفهرس - Index";
    
    with (indexTitle.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.headerFont);
        pointSize = textStyles.headerSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createPrimaryColor(doc);
    }
    
    // محتويات الفهرس
    var indexContent = indexPage.textFrames.add();
    indexContent.geometricBounds = [
        parseFloat(catalogSettings.margins.top) + 2,
        parseFloat(catalogSettings.margins.left),
        parseFloat(catalogSettings.pageHeight) - parseFloat(catalogSettings.margins.bottom),
        parseFloat(catalogSettings.pageWidth) - parseFloat(catalogSettings.margins.right)
    ];
    
    var indexText = "الفئات - Categories:\n\n";
    indexText += "الإلكترونيات ............................ صفحة 3\n";
    indexText += "الملابس ................................. صفحة 7\n";
    indexText += "المنزل والحديقة ...................... صفحة 11\n";
    indexText += "الرياضة واللياقة ...................... صفحة 15\n";
    indexText += "الكتب والقرطاسية ..................... صفحة 19\n\n";
    indexText += "Electronics ............................ Page 3\n";
    indexText += "Clothing ............................... Page 7\n";
    indexText += "Home & Garden .......................... Page 11\n";
    indexText += "Sports & Fitness ....................... Page 15\n";
    indexText += "Books & Stationery ..................... Page 19\n";
    
    indexContent.contents = indexText;
    
    with (indexContent.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.indexFont);
        pointSize = textStyles.indexSize;
        justification = Justification.LEFT_ALIGN;
        fillColor = createTextColor(doc);
    }
}

function createProductPages(doc) {
    var startPageIndex = 0;
    if (catalogSettings.includeCover) startPageIndex++;
    if (catalogSettings.includeIndex) startPageIndex++;
    
    for (var i = 0; i < catalogSettings.numberOfPages; i++) {
        var pageIndex = startPageIndex + i;
        if (pageIndex < doc.pages.length) {
            createProductPage(doc, doc.pages[pageIndex], i + 1);
        }
    }
}

function createProductPage(doc, page, pageNumber) {
    // إنشاء رأس الصفحة
    createPageHeader(doc, page, "صفحة المنتجات " + pageNumber);
    
    // إنشاء شبكة المنتجات (2x3)
    var availableWidth = parseFloat(catalogSettings.pageWidth) - 
                        parseFloat(catalogSettings.margins.left) - 
                        parseFloat(catalogSettings.margins.right);
    var availableHeight = parseFloat(catalogSettings.pageHeight) - 
                         parseFloat(catalogSettings.margins.top) - 
                         parseFloat(catalogSettings.margins.bottom) - 
                         parseFloat(designSettings.headerHeight) - 
                         parseFloat(designSettings.footerHeight);
    
    var cellWidth = availableWidth / 2 - 0.5;
    var cellHeight = availableHeight / 3 - 0.5;
    
    for (var row = 0; row < 3; row++) {
        for (var col = 0; col < 2; col++) {
            createCatalogProductCell(doc, page, row, col, cellWidth, cellHeight);
        }
    }
    
    // إنشاء تذييل الصفحة
    createPageFooter(doc, page);
}

function createCatalogProductCell(doc, page, row, col, cellWidth, cellHeight) {
    var startX = parseFloat(catalogSettings.margins.left) + col * (cellWidth + 0.5);
    var startY = parseFloat(catalogSettings.margins.top) + parseFloat(designSettings.headerHeight) +
                 row * (cellHeight + 0.5);

    // إطار المنتج
    var productFrame = page.rectangles.add();
    productFrame.geometricBounds = [startY, startX, startY + cellHeight, startX + cellWidth];
    productFrame.strokeWeight = "1pt";
    productFrame.strokeColor = createSecondaryColor(doc);
    productFrame.fillColor = doc.colors.item("Paper");

    // إطار الصورة
    var imageHeight = cellHeight * 0.5;
    var imageFrame = page.rectangles.add();
    imageFrame.geometricBounds = [startY + 0.3, startX + 0.3, startY + imageHeight, startX + cellWidth - 0.3];
    imageFrame.strokeWeight = "0.5pt";
    imageFrame.strokeColor = createSecondaryColor(doc);
    imageFrame.fillColor = createSecondaryColor(doc);

    // نص الصورة
    var imageText = imageFrame.textFrames.add();
    imageText.contents = "صورة المنتج\nProduct Image";
    with (imageText.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "9pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createTextColor(doc);
    }

    // اسم المنتج
    var nameFrame = page.textFrames.add();
    nameFrame.geometricBounds = [
        startY + imageHeight + 0.3,
        startX + 0.3,
        startY + imageHeight + 1.2,
        startX + cellWidth - 0.3
    ];
    nameFrame.contents = "اسم المنتج " + (row * 2 + col + 1);
    with (nameFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.productNameFont);
        pointSize = textStyles.productNameSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createTextColor(doc);
    }

    // وصف المنتج
    var descFrame = page.textFrames.add();
    descFrame.geometricBounds = [
        startY + imageHeight + 1.2,
        startX + 0.3,
        startY + cellHeight - 1.5,
        startX + cellWidth - 0.3
    ];
    descFrame.contents = "وصف تفصيلي للمنتج يشمل المواصفات والميزات الرئيسية.";
    with (descFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "9pt";
        justification = Justification.LEFT_ALIGN;
        fillColor = createTextColor(doc);
    }

    // السعر
    var priceFrame = page.textFrames.add();
    priceFrame.geometricBounds = [
        startY + cellHeight - 1.5,
        startX + 0.3,
        startY + cellHeight - 0.3,
        startX + cellWidth - 0.3
    ];
    priceFrame.contents = "السعر: " + (Math.floor(Math.random() * 1000) + 100) + " ج.م";
    with (priceFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.productNameFont);
        pointSize = "12pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createAccentColor(doc);
    }
}

function createPageHeader(doc, page, headerText) {
    var headerFrame = page.textFrames.add();
    headerFrame.geometricBounds = [
        parseFloat(catalogSettings.margins.top),
        parseFloat(catalogSettings.margins.left),
        parseFloat(catalogSettings.margins.top) + parseFloat(designSettings.headerHeight),
        parseFloat(catalogSettings.pageWidth) - parseFloat(catalogSettings.margins.right)
    ];

    // خلفية الرأس
    var headerBg = page.rectangles.add();
    headerBg.geometricBounds = headerFrame.geometricBounds;
    headerBg.fillColor = createPrimaryColor(doc);
    headerBg.strokeColor = doc.colors.item("None");
    headerBg.sendToBack();

    headerFrame.contents = headerText;
    with (headerFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.headerFont);
        pointSize = textStyles.headerSize;
        justification = Justification.CENTER_ALIGN;
        fillColor = doc.colors.item("Paper");
    }
}

function createPageFooter(doc, page) {
    var footerFrame = page.textFrames.add();
    footerFrame.geometricBounds = [
        parseFloat(catalogSettings.pageHeight) - parseFloat(catalogSettings.margins.bottom) - parseFloat(designSettings.footerHeight),
        parseFloat(catalogSettings.margins.left),
        parseFloat(catalogSettings.pageHeight) - parseFloat(catalogSettings.margins.bottom),
        parseFloat(catalogSettings.pageWidth) - parseFloat(catalogSettings.margins.right)
    ];

    footerFrame.contents = designSettings.companyName + " | www.company.com | <EMAIL>";
    with (footerFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = "9pt";
        justification = Justification.CENTER_ALIGN;
        fillColor = createSecondaryColor(doc);
    }
}

function createBackCover(doc) {
    var backCoverPage = doc.pages[doc.pages.length - 1];

    // خلفية الغلاف الخلفي
    var backCoverBg = backCoverPage.rectangles.add();
    backCoverBg.geometricBounds = backCoverPage.bounds;
    backCoverBg.fillColor = createSecondaryColor(doc);
    backCoverBg.strokeColor = doc.colors.item("None");

    // معلومات الاتصال
    var contactFrame = backCoverPage.textFrames.add();
    contactFrame.geometricBounds = [
        parseFloat(catalogSettings.pageHeight) * 0.3,
        parseFloat(catalogSettings.margins.left),
        parseFloat(catalogSettings.pageHeight) * 0.7,
        parseFloat(catalogSettings.pageWidth) - parseFloat(catalogSettings.margins.right)
    ];

    var contactText = "تواصل معنا - Contact Us\n\n";
    contactText += "العنوان: شارع التجارة، المدينة\n";
    contactText += "الهاتف: +20 ************\n";
    contactText += "البريد الإلكتروني: <EMAIL>\n";
    contactText += "الموقع الإلكتروني: www.company.com\n\n";
    contactText += "Address: Commerce Street, City\n";
    contactText += "Phone: +20 ************\n";
    contactText += "Email: <EMAIL>\n";
    contactText += "Website: www.company.com";

    contactFrame.contents = contactText;
    with (contactFrame.parentStory.characters.everyItem()) {
        appliedFont = app.fonts.item(textStyles.bodyFont);
        pointSize = textStyles.bodySize;
        justification = Justification.CENTER_ALIGN;
        fillColor = createTextColor(doc);
    }
}

function addPageNumbers(doc) {
    for (var i = 1; i < doc.pages.length - 1; i++) { // تجاهل الغلاف والغلاف الخلفي
        var page = doc.pages[i];
        var pageNumFrame = page.textFrames.add();
        pageNumFrame.geometricBounds = [
            parseFloat(catalogSettings.pageHeight) - parseFloat(catalogSettings.margins.bottom) + 0.2,
            parseFloat(catalogSettings.pageWidth) / 2 - 0.5,
            parseFloat(catalogSettings.pageHeight) - parseFloat(catalogSettings.margins.bottom) + 0.7,
            parseFloat(catalogSettings.pageWidth) / 2 + 0.5
        ];

        pageNumFrame.contents = String(i);
        with (pageNumFrame.parentStory.characters.everyItem()) {
            appliedFont = app.fonts.item(textStyles.bodyFont);
            pointSize = "10pt";
            justification = Justification.CENTER_ALIGN;
            fillColor = createTextColor(doc);
        }
    }
}

// دوال الألوان
function createPrimaryColor(doc) {
    try {
        return doc.colors.item("Primary");
    } catch (e) {
        var color = doc.colors.add();
        color.name = "Primary";
        color.model = ColorModel.PROCESS;
        color.colorValue = catalogColors.primary;
        return color;
    }
}

function createSecondaryColor(doc) {
    try {
        return doc.colors.item("Secondary");
    } catch (e) {
        var color = doc.colors.add();
        color.name = "Secondary";
        color.model = ColorModel.PROCESS;
        color.colorValue = catalogColors.secondary;
        return color;
    }
}

function createAccentColor(doc) {
    try {
        return doc.colors.item("Accent");
    } catch (e) {
        var color = doc.colors.add();
        color.name = "Accent";
        color.model = ColorModel.PROCESS;
        color.colorValue = catalogColors.accent;
        return color;
    }
}

function createTextColor(doc) {
    try {
        return doc.colors.item("TextColor");
    } catch (e) {
        var color = doc.colors.add();
        color.name = "TextColor";
        color.model = ColorModel.PROCESS;
        color.colorValue = catalogColors.text;
        return color;
    }
}

// تشغيل السكربت
main();
