# 🔧 دليل التثبيت والإعداد
## Installation & Setup Guide

---

## 📋 المتطلبات / Requirements

### البرامج المطلوبة:
- ✅ **Adobe InDesign CS6** أو أحدث
- ✅ **نظام التشغيل**: Windows 10/11 أو macOS 10.12+
- ✅ **ذاكرة الوصول العشوائي**: 4 جيجابايت كحد أدنى
- ✅ **مساحة القرص الصلب**: 100 ميجابايت للسكربتات

### الخطوط المطلوبة:
- Arial (متوفر افتراضياً)
- Arial Bold (متوفر افتراضياً)
- خطوط عربية إضافية (اختيارية)

---

## 🚀 خطوات التثبيت / Installation Steps

### الطريقة الأولى: التثبيت السريع
1. **تحميل الملفات**
   - احفظ جميع ملفات `.jsx` في مجلد واحد
   - تأكد من وجود جميع الملفات الـ 8

2. **تشغيل مباشر**
   - افتح Adobe InDesign
   - اسحب ملف `indesign_master_toolkit.jsx` إلى نافذة InDesign
   - اختر الأداة المطلوبة من القائمة

### الطريقة الثانية: التثبيت الدائم
1. **العثور على مجلد Scripts**
   ```
   Windows: C:\Users\<USER>\AppData\Roaming\Adobe\InDesign\[الإصدار]\Scripts\Scripts Panel
   macOS: ~/Library/Preferences/Adobe InDesign/[الإصدار]/Scripts/Scripts Panel
   ```

2. **نسخ الملفات**
   - انسخ جميع ملفات `.jsx` إلى مجلد Scripts Panel
   - أعد تشغيل InDesign

3. **الوصول للسكربتات**
   - اذهب إلى `Window > Utilities > Scripts`
   - ستجد السكربتات في قسم User
   - انقر نقرة مزدوجة لتشغيل أي سكربت

---

## ⚡ الاستخدام السريع / Quick Start

### للمبتدئين:
1. ابدأ بـ `indesign_master_toolkit.jsx`
2. اختر "شبكة المنتجات الأساسية"
3. اتبع التعليمات المنبثقة
4. احفظ النتيجة

### للمحترفين:
1. استخدم السكربتات المنفصلة مباشرة
2. خصص الإعدادات في بداية كل ملف
3. استخدم `excel_data_importer_script.jsx` للبيانات الكبيرة
4. استخدم `advanced_export_tools_script.jsx` للتصدير المتقدم

---

## 🎨 أمثلة عملية / Practical Examples

### مثال 1: إنشاء كتالوج منتجات
```
1. شغل catalog_generator_script.jsx
2. اختر عدد الصفحات (20 صفحة)
3. فعل خيار "تضمين فهرس"
4. اختر مخطط ألوان "عصري"
5. انقر "إنشاء"
```

### مثال 2: فلاير تخفيضات
```
1. شغل flyer_generator_script.jsx
2. اختر "فلاير تخفيضات"
3. أدخل نص العرض
4. اختر مخطط ألوان "نابض"
5. انقر "إنشاء"
```

### مثال 3: استيراد من Excel
```
1. حضر ملف CSV بالتنسيق المطلوب
2. شغل excel_data_importer_script.jsx
3. اختر الملف
4. حدد نوع القالب
5. انقر "استيراد"
```

---

## 🔍 استكشاف الأخطاء / Troubleshooting

### المشاكل الشائعة والحلول:

#### ❌ "السكربت لا يعمل"
**الحلول:**
- تحقق من إصدار InDesign (CS6+)
- تأكد من تفعيل JavaScript في InDesign
- أعد تشغيل InDesign وجرب مرة أخرى

#### ❌ "خطأ في الخط"
**الحلول:**
- السكربت سيستخدم Arial تلقائياً
- تأكد من تثبيت الخطوط العربية
- عدل اسم الخط في إعدادات السكربت

#### ❌ "خطأ في الألوان"
**الحلول:**
- السكربت سينشئ الألوان تلقائياً
- تحقق من إعدادات الألوان في InDesign
- استخدم مخطط ألوان مختلف

#### ❌ "فشل في الحفظ"
**الحلول:**
- تحقق من صلاحيات المجلد
- تأكد من وجود مساحة كافية
- جرب مجلد حفظ مختلف

#### ❌ "بطء في الأداء"
**الحلول:**
- قلل عدد العناصر في الصفحة
- استخدم جودة أقل للمعاينة
- أغلق البرامج الأخرى

---

## 📈 تحسين الأداء / Performance Optimization

### نصائح لتسريع العمل:
1. **استخدم SSD** بدلاً من HDD
2. **أغلق البرامج غير المستخدمة**
3. **زد ذاكرة InDesign** من التفضيلات
4. **استخدم دقة منخفضة** للمعاينة
5. **احفظ بانتظام** لتجنب فقدان البيانات

### إعدادات InDesign المُحسنة:
```
Edit > Preferences > Performance:
- GPU Performance: تفعيل
- Display Performance: Fast Display
- Memory Usage: 70% للـ InDesign
```

---

## 🎓 دروس متقدمة / Advanced Tutorials

### تخصيص السكربتات:
1. افتح أي ملف `.jsx` في محرر نصوص
2. ابحث عن قسم "إعدادات" في بداية الملف
3. عدل القيم حسب احتياجاتك
4. احفظ الملف

### إضافة خطوط جديدة:
```javascript
// في قسم textStyles
titleFont: "اسم الخط الجديد",
// مثال:
titleFont: "Cairo-Bold",
```

### تغيير الألوان:
```javascript
// في قسم colorSchemes
primary: [C, M, Y, K], // قيم CMYK
// مثال:
primary: [0, 100, 100, 0], // أحمر نقي
```

---

## 📞 الدعم الفني / Technical Support

### قبل طلب المساعدة:
1. تحقق من هذا الدليل
2. جرب على مستند جديد
3. تأكد من إصدار InDesign
4. احفظ رسالة الخطأ (إن وجدت)

### معلومات مفيدة للدعم:
- إصدار InDesign
- نظام التشغيل
- رسالة الخطأ الكاملة
- الخطوات التي أدت للمشكلة

---

**🎉 مبروك! أصبحت جاهزاً لاستخدام مجموعة أدوات InDesign الاحترافية!**
